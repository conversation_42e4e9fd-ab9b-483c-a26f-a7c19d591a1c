# ATS CV Generator - Deployment Guide

This guide covers deployment options for the ATS CV Generator system.

## Quick Start Deployment

### 1. Local Development Setup
```bash
# Clone and setup
git clone <repository>
cd hiring-cv-generator

# Install dependencies
make install-dev

# Create sample templates
make create-templates

# Set API key
export GEMINI_API_KEY="your-gemini-api-key"

# Run tests
make test

# Start API server
make run-api
```

### 2. Production Deployment

#### Environment Variables
```bash
# Required
export GEMINI_API_KEY="your-production-api-key"

# Optional
export CV_GENERATOR_LOG_LEVEL="INFO"
export CV_GENERATOR_OUTPUT_DIR="/data/generated_resumes"
export CV_GENERATOR_MAX_CONCURRENT="8"
```

#### System Requirements
- **CPU**: 4+ cores recommended
- **RAM**: 8GB+ for large batch processing
- **Storage**: 10GB+ free space
- **OS**: Linux (Ubuntu 20.04+ recommended)

## Docker Deployment

### 1. Build Docker Image
```dockerfile
# Dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    chromium \
    wkhtmltopdf \
    fonts-liberation \
    fonts-dejavu-core \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 cvgen && chown -R cvgen:cvgen /app
USER cvgen

# Expose port
EXPOSE 8000

# Start API server
CMD ["python", "-m", "src.api.server"]
```

### 2. Build and Run
```bash
# Build image
docker build -t ats-cv-generator .

# Run container
docker run -d \
  --name cv-generator \
  -p 8000:8000 \
  -e GEMINI_API_KEY="your-api-key" \
  -v /host/output:/app/generated_resumes \
  ats-cv-generator
```

### 3. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  cv-generator:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CV_GENERATOR_LOG_LEVEL=INFO
    volumes:
      - ./output:/app/generated_resumes
      - ./logs:/app/logs
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - cv-generator
    restart: unless-stopped
```

## Cloud Deployment

### 1. AWS Deployment

#### EC2 Instance
```bash
# Launch EC2 instance (t3.large or larger)
# Install Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# Deploy application
git clone <repository>
cd hiring-cv-generator
docker build -t ats-cv-generator .
docker run -d -p 8000:8000 -e GEMINI_API_KEY="$GEMINI_API_KEY" ats-cv-generator
```

#### ECS Deployment
```json
{
  "family": "ats-cv-generator",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "cv-generator",
      "image": "your-account.dkr.ecr.region.amazonaws.com/ats-cv-generator:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "GEMINI_API_KEY",
          "value": "your-api-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/ats-cv-generator",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 2. Google Cloud Platform

#### Cloud Run Deployment
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/ats-cv-generator

# Deploy to Cloud Run
gcloud run deploy ats-cv-generator \
  --image gcr.io/PROJECT-ID/ats-cv-generator \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars GEMINI_API_KEY="your-api-key" \
  --memory 2Gi \
  --cpu 2
```

### 3. Azure Container Instances
```bash
# Create resource group
az group create --name cv-generator-rg --location eastus

# Deploy container
az container create \
  --resource-group cv-generator-rg \
  --name ats-cv-generator \
  --image your-registry/ats-cv-generator:latest \
  --cpu 2 \
  --memory 4 \
  --ports 8000 \
  --environment-variables GEMINI_API_KEY="your-api-key" \
  --restart-policy Always
```

## Kubernetes Deployment

### 1. Deployment Manifest
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ats-cv-generator
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ats-cv-generator
  template:
    metadata:
      labels:
        app: ats-cv-generator
    spec:
      containers:
      - name: cv-generator
        image: ats-cv-generator:latest
        ports:
        - containerPort: 8000
        env:
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: cv-generator-secrets
              key: gemini-api-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: output-storage
          mountPath: /app/generated_resumes
      volumes:
      - name: output-storage
        persistentVolumeClaim:
          claimName: cv-generator-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: ats-cv-generator-service
spec:
  selector:
    app: ats-cv-generator
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

### 2. Deploy to Kubernetes
```bash
# Create secret for API key
kubectl create secret generic cv-generator-secrets \
  --from-literal=gemini-api-key="your-api-key"

# Apply deployment
kubectl apply -f k8s-deployment.yaml

# Check status
kubectl get pods
kubectl get services
```

## Monitoring and Logging

### 1. Application Monitoring
```python
# Add to config.yaml
logging:
  level: "INFO"
  file: "/var/log/cv_generator.log"
  max_file_size_mb: 100
  backup_count: 10

monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30
```

### 2. Prometheus Metrics
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ats-cv-generator'
    static_configs:
      - targets: ['localhost:9090']
```

### 3. Log Aggregation
```bash
# Using ELK Stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.14.0

docker run -d \
  --name logstash \
  -p 5000:5000 \
  -v ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf \
  logstash:7.14.0

docker run -d \
  --name kibana \
  -p 5601:5601 \
  -e "ELASTICSEARCH_HOSTS=http://elasticsearch:9200" \
  kibana:7.14.0
```

## Security Considerations

### 1. API Security
- Use HTTPS in production
- Implement rate limiting
- Add authentication/authorization
- Validate all inputs
- Sanitize file uploads

### 2. Environment Security
```bash
# Use secrets management
export GEMINI_API_KEY=$(aws secretsmanager get-secret-value \
  --secret-id prod/cv-generator/gemini-key \
  --query SecretString --output text)

# Restrict file permissions
chmod 600 config.yaml
chown app:app config.yaml
```

### 3. Network Security
```bash
# Firewall rules
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 8000/tcp   # Block direct API access
ufw enable
```

## Performance Optimization

### 1. Scaling Configuration
```yaml
# config.yaml
processing:
  max_concurrent_threads: 16  # Increase for more CPU cores
  batch_size: 20              # Larger batches for efficiency

gemini:
  rate_limit: 120             # Increase if API allows

pdf:
  timeout: 60                 # Increase for complex documents
```

### 2. Caching
```python
# Add Redis caching for templates
REDIS_URL = "redis://localhost:6379"
CACHE_TTL = 3600  # 1 hour
```

### 3. Load Balancing
```nginx
# nginx.conf
upstream cv_generator {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    location / {
        proxy_pass http://cv_generator;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Backup and Recovery

### 1. Data Backup
```bash
# Backup generated resumes
rsync -av /data/generated_resumes/ /backup/resumes/

# Backup configuration
cp config.yaml /backup/config/config-$(date +%Y%m%d).yaml

# Database backup (if using)
pg_dump cv_generator > /backup/db/cv_generator-$(date +%Y%m%d).sql
```

### 2. Disaster Recovery
```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/cv-generator-$DATE"

mkdir -p $BACKUP_DIR
cp -r generated_resumes/ $BACKUP_DIR/
cp config.yaml $BACKUP_DIR/
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR/
rm -rf $BACKUP_DIR

# Upload to cloud storage
aws s3 cp $BACKUP_DIR.tar.gz s3://cv-generator-backups/
```

## Troubleshooting

### Common Issues
1. **API Rate Limits**: Reduce concurrent threads or increase delays
2. **Memory Issues**: Increase container memory or reduce batch size
3. **PDF Generation Fails**: Check Chrome/wkhtmltopdf installation
4. **Template Upload Errors**: Verify file formats and sizes

### Health Checks
```bash
# API health check
curl http://localhost:8000/health

# System resources
docker stats cv-generator

# Application logs
docker logs cv-generator --tail 100
```
