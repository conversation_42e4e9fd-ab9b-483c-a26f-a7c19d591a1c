# ATS CV Generator - Makefile

.PHONY: help install test lint format clean run-api run-cli create-templates docs

# Default target
help:
	@echo "ATS CV Generator - Available Commands"
	@echo "===================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo ""
	@echo "Development Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  lint             Run code linting"
	@echo "  format           Format code with black"
	@echo "  type-check       Run type checking with mypy"
	@echo ""
	@echo "Application Commands:"
	@echo "  run-api          Start the REST API server"
	@echo "  run-cli          Run CLI with example parameters"
	@echo "  create-templates Create sample templates"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean            Clean up generated files"
	@echo "  docs             Generate documentation"
	@echo "  validate-config  Validate configuration"

# Setup commands
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install pytest pytest-asyncio pytest-mock black flake8 mypy

# Development commands
test:
	pytest tests/ -v

test-unit:
	pytest tests/ -v -m "unit"

test-integration:
	pytest tests/ -v -m "integration"

test-coverage:
	pytest tests/ --cov=src --cov-report=html --cov-report=term

lint:
	flake8 src/ tests/ --max-line-length=100 --ignore=E203,W503

format:
	black src/ tests/ examples/ --line-length=100

type-check:
	mypy src/ --ignore-missing-imports

# Application commands
run-api:
	python -m src.api.server

run-cli:
	@echo "Running CLI with example parameters..."
	@echo "Make sure you have templates in ./templates/ directory"
	python generate_resumes.py \
		--templates ./templates/ \
		--count 5 \
		--distribution "70,20,10" \
		--job-desc "Software Engineer position requiring Python and JavaScript experience. 3+ years experience preferred." \
		--dry-run

create-templates:
	python examples/create_sample_templates.py --output-dir templates

# Utility commands
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	rm -rf generated_resumes_*/
	rm -f cv_generator.log*

docs:
	@echo "Documentation available in docs/ directory:"
	@echo "  - docs/SETUP.md      Setup and installation guide"
	@echo "  - docs/API.md        API reference and CLI usage"
	@echo "  - README.md          Project overview"

validate-config:
	python -c "from src.validation import ConfigValidator; result = ConfigValidator.validate_config(); print('Config valid:', result.is_valid); [print('Error:', e) for e in result.errors]; [print('Warning:', w) for w in result.warnings]"

# Development workflow
dev-setup: install-dev create-templates
	@echo "Development environment setup complete!"
	@echo "Next steps:"
	@echo "1. Set your Gemini API key: export GEMINI_API_KEY='your-key'"
	@echo "2. Run tests: make test"
	@echo "3. Try the CLI: make run-cli"

# CI/CD targets
ci-test: lint type-check test

# Docker targets (if needed)
docker-build:
	docker build -t ats-cv-generator .

docker-run:
	docker run -p 8000:8000 -e GEMINI_API_KEY=${GEMINI_API_KEY} ats-cv-generator

# Package targets
package:
	python setup.py sdist bdist_wheel

upload-test:
	twine upload --repository testpypi dist/*

upload:
	twine upload dist/*
