# ATS CV Generator

A comprehensive CV/resume generator system for testing ATS (Applicant Tracking System) robustness and parsing capabilities with configurable parameters and distribution ratios.

## Features

- **AI-Powered Generation**: Integrates with Google Gemini Vision API for template-based resume generation
- **Configurable Distribution**: Generate resumes with custom ratios of high-quality, edge case, and malicious content
- **Template Support**: Upload 1-50 JPEG templates as visual references
- **Batch Processing**: Multi-threaded generation with progress tracking
- **PDF Conversion**: HTML to PDF conversion using Puppeteer
- **CLI & API**: Command-line interface and optional REST API
- **Comprehensive Testing**: Generate datasets for ATS parsing edge cases and security vulnerabilities

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set API Key**:
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key"
   ```

3. **Generate Resumes**:
   ```bash
   python generate_resumes.py --templates ./templates/ --count 100 --distribution 70,20,10 --job-desc "Software Engineer position..."
   ```

## Project Structure

```
├── src/
│   ├── __init__.py
│   ├── config.py          # Configuration management
│   ├── models.py          # Data models
│   ├── utils.py           # Utility functions
│   ├── gemini_client.py   # Gemini API integration
│   ├── template_processor.py  # Template handling
│   ├── resume_generator.py    # Core generation logic
│   ├── pdf_converter.py   # PDF conversion
│   ├── batch_processor.py # Batch processing
│   └── api/               # REST API (optional)
├── templates/             # Example templates
├── tests/                 # Test suite
├── config.yaml           # Configuration file
├── requirements.txt       # Dependencies
└── generate_resumes.py   # CLI entry point
```

## Configuration

Edit `config.yaml` to customize:
- Gemini API settings
- Template processing options
- Generation parameters
- PDF conversion settings
- Output structure

## Usage Examples

### CLI Usage
```bash
# Basic generation
python generate_resumes.py --templates ./templates/ --count 500 --distribution 80,15,5 --job-desc "Python Developer role..."

# With custom output directory
python generate_resumes.py --templates ./templates/ --count 200 --distribution 75,15,10 --job-desc "Data Scientist position..." --output ./custom_output/
```

### API Usage
```bash
# Start API server
python -m src.api.server

# Generate resumes via API
curl -X POST "http://localhost:8000/generate" \
  -F "templates=@template1.jpg" \
  -F "templates=@template2.jpg" \
  -F "total_count=100" \
  -F "distribution={\"high_quality\":70,\"edge_cases\":20,\"malicious\":10}" \
  -F "job_description=Software Engineer position..."
```

## Output Structure

```
/generated_resumes_[timestamp]/
  /high_quality/     # Professional, ATS-friendly resumes
  /edge_cases/       # Non-standard formatting, multilingual
  /malicious/        # Adversarial content for security testing
  /templates_used/   # Copies of uploaded templates
  /generation_report.json  # Detailed statistics and metadata
```

## Resume Types

### High-Quality Resumes (Default: 80%)
- ATS-friendly formatting
- Standard section headers
- Optimal keyword density
- Professional fonts and layout
- Clean semantic HTML

### Edge Case Resumes (Default: 10%)
- Non-standard HTML structures
- Multilingual content
- Unicode characters and symbols
- Unconventional section naming
- Complex CSS layouts

### Malicious/Adversarial Resumes (Default: 10%)
- Prompt injection attempts
- Hidden text techniques
- Code injection payloads
- Buffer overflow tests
- Steganographic content

## Requirements

- Python 3.8+
- Google Gemini API key
- Node.js (for Puppeteer PDF conversion)
- 2GB+ RAM for batch processing
- 1GB+ disk space for output

## License

MIT License - see LICENSE file for details.
