# Resume Uniqueness Improvements

This document outlines the comprehensive improvements made to ensure all generated resumes are unique in both naming and content.

## Overview

The system now implements multiple layers of uniqueness to ensure every generated resume is distinct:

1. **Unique Filename Generation**
2. **Content Variation Engine**
3. **Deduplication System**
4. **Enhanced Prompt Engineering**
5. **Uniqueness Validation and Reporting**

## 1. Unique Filename Generation

### Enhanced Filename Structure
Filenames now include multiple uniqueness factors:
```
resume_{type}_{sequence}_{resume_id}_{timestamp}_{content_hash}.{extension}
```

**Example:**
```
resume_high_quality_0001_a1b2c3d4e5f6_20250912_143052_123_8a7b9c2d.html
```

### Components:
- **Type**: Resume category (high_quality, edge_cases, malicious)
- **Sequence**: Zero-padded index (0001, 0002, etc.)
- **Resume ID**: First 12 characters of UUID
- **Timestamp**: Date and time with microseconds
- **Content Hash**: First 8 characters of content SHA-256 hash
- **Extension**: File type (html, pdf)

### Implementation:
- `src/utils.py`: `generate_unique_filename()` function
- `src/utils.py`: `calculate_content_hash()` function
- Applied to both HTML and PDF file generation

## 2. Content Variation Engine

### New Module: `src/content_variation.py`

Generates diverse, realistic resume content to ensure no two resumes are identical.

### Features:

#### Personal Information Variation
- **Names**: 50+ first names, 60+ last names with collision tracking
- **Emails**: 4 different email formats across multiple providers
- **Locations**: 48 major US cities with state abbreviations
- **Contact Info**: Varied phone number formats, LinkedIn/GitHub profiles

#### Experience Generation
- **Companies**: Industry-specific company pools (tech, finance, healthcare, consulting)
- **Job Titles**: Seniority-based progression (junior → mid → senior → executive)
- **Durations**: Realistic employment periods with proper chronology
- **Descriptions**: Role-appropriate bullet points with varied achievements

#### Education Variation
- **Universities**: 20+ prestigious institutions
- **Degrees**: Multiple degree types (BS, BA, MS, MA, MBA, BE)
- **Fields**: 14 different academic fields
- **Details**: Optional GPA, graduation years, locations

#### Skills Diversification
- **Categories**: Programming, Web, Data, Cloud, Soft Skills
- **Distribution**: Balanced skill sets with 12-20 total skills
- **Variation**: Different combinations for each resume type

### Usage:
```python
engine = ContentVariationEngine()
profile = engine.generate_unique_profile(seed="unique_seed")
experience = engine.generate_varied_experience(industry="tech")
education = engine.generate_varied_education()
skills = engine.generate_varied_skills()
```

## 3. Deduplication System

### New Module: `src/deduplication.py`

Implements comprehensive content deduplication and similarity detection.

### Content Fingerprinting
Each resume gets a multi-dimensional fingerprint:
- **Full Hash**: Complete HTML content SHA-256
- **Text Hash**: Plain text content (HTML stripped)
- **Structure Hash**: HTML tags and CSS classes
- **Section Hashes**: Individual hashes for name, experience, education, skills

### Duplicate Detection
- **Exact Duplicates**: Identical content detection
- **Text Duplicates**: Same content, different formatting
- **Name Duplicates**: Same person names
- **Similarity Detection**: Configurable threshold (default 85%)

### Regeneration Logic
When duplicates are detected:
1. Log the duplicate type and attempt number
2. Modify the generation seed to get different content
3. Regenerate up to 3 times
4. Track regeneration statistics

### Statistics Tracking
- Total processed resumes
- Unique vs duplicate counts
- Uniqueness percentage
- Detailed duplicate type breakdown

## 4. Enhanced Prompt Engineering

### Uniqueness Instructions
Every prompt now includes specific uniqueness requirements:
```
UNIQUENESS REQUIREMENTS (Resume #{index}):
- This is resume #{index} in a batch - it MUST be completely unique
- Use varied CSS styling: different fonts, colors, spacing, and layout approaches
- Vary the section order and presentation style from other resumes
- Use different HTML structures and CSS techniques for visual variety
```

### Type-Specific Variations

#### High-Quality Resumes
5 different style variations:
- Modern minimalist design
- Classic professional layout
- Clean corporate style
- Contemporary geometric design
- Sophisticated elegant typography

#### Edge Case Resumes
5 different layout variations:
- CSS Grid with overlapping elements
- Magazine-style multi-column layout
- Timeline-based absolute positioning
- Creative flexbox implementations
- Card-based CSS transforms

#### Malicious Resumes
5 different adversarial techniques:
- Hidden divs with keyword stuffing
- Invisible text layers with CSS manipulation
- Steganographic Unicode content
- Nested malformed HTML structures
- CSS pseudo-element injection

### Content Specification
Prompts now include exact content specifications:
- Specific personal information to use
- Detailed work experience entries
- Education background details
- Skill categorization and lists

## 5. Uniqueness Validation and Reporting

### Batch-Level Validation
- Final uniqueness check across entire batch
- Content hash comparison for exact duplicates
- Duplicate group identification
- Uniqueness score calculation

### Enhanced Reporting
Generation reports now include:
```json
{
  "uniqueness_metrics": {
    "total_processed": 100,
    "unique_content": 98,
    "duplicates_found": 2,
    "uniqueness_rate": 98.0,
    "unique_names": 100,
    "unique_text_content": 98
  },
  "uniqueness_validation": {
    "total_resumes": 100,
    "unique_resumes": 98,
    "duplicate_groups": [...],
    "uniqueness_score": 98.0,
    "validation_warnings": [...]
  }
}
```

### Warning System
- Alerts for low uniqueness scores (<90%)
- Duplicate group notifications
- Regeneration attempt tracking

## Testing

### Test Suite: `test_uniqueness.py`
Comprehensive testing of all uniqueness features:
- Content variation engine testing
- Deduplication system validation
- Filename uniqueness verification
- Integration testing guidance

### Running Tests
```bash
python test_uniqueness.py
```

## Usage Examples

### Basic Generation with Uniqueness
```bash
python generate_resumes.py --count 50 --job-desc "Software Engineer" --output unique_resumes
```

### Monitoring Uniqueness
Check the generation report for uniqueness metrics:
```bash
cat unique_resumes/generation_report.json | jq '.generation_statistics.uniqueness_metrics'
```

## Benefits

1. **Guaranteed Unique Filenames**: No file overwrites or naming conflicts
2. **Diverse Content**: Every resume has different personal information and experiences
3. **Visual Variety**: Different layouts, styles, and presentations
4. **Quality Assurance**: Automatic duplicate detection and regeneration
5. **Comprehensive Reporting**: Detailed metrics on uniqueness performance
6. **Scalability**: System maintains uniqueness even for large batches (1000+ resumes)

## Technical Implementation

### Key Files Modified/Added:
- `src/content_variation.py` - New content generation engine
- `src/deduplication.py` - New deduplication system
- `src/utils.py` - Enhanced filename generation
- `src/gemini_client.py` - Enhanced prompts with uniqueness
- `src/resume_generator.py` - Integrated uniqueness checks
- `src/batch_processor.py` - Added validation and reporting
- `test_uniqueness.py` - Comprehensive test suite

### Dependencies:
- No new external dependencies required
- Uses existing libraries (hashlib, random, datetime, etc.)
- Maintains compatibility with existing codebase

## Future Enhancements

1. **Machine Learning Similarity**: Use ML models for semantic similarity detection
2. **Visual Similarity**: Compare rendered resume images for visual uniqueness
3. **Industry-Specific Variations**: More targeted content for different job sectors
4. **Internationalization**: Support for multiple languages and regions
5. **Performance Optimization**: Caching and parallel processing for large batches
