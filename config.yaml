# ATS CV Generator Configuration

# Gemini API Configuration
gemini:
  api_key: ""  # Set via environment variable GEMINI_API_KEY
  model: "gemini-2.0-flash"
  max_retries: 3
  retry_delays: [1, 2, 4]  # seconds
  rate_limit: 60  # requests per minute
  timeout: 30  # seconds

# Template Configuration
templates:
  max_count: 50
  max_file_size_mb: 10
  supported_formats: ["jpg", "jpeg"]
  categories:
    - simple
    - complex
    - creative
    - corporate
    - minimalist
    - industry_specific

# Generation Configuration
generation:
  default_distribution:
    high_quality: 80
    edge_cases: 10
    malicious: 10
  max_total_count: 10000
  min_job_description_length: 100
  max_job_description_length: 3000
  
# Processing Configuration
processing:
  max_concurrent_threads: 4
  batch_size: 10
  progress_update_interval: 5  # seconds

# PDF Configuration
pdf:
  page_format: "A4"
  margin: "0.5in"
  print_background: true
  wait_for_fonts: true
  timeout: 30  # seconds

# Output Configuration
output:
  base_directory: "./generated_resumes"
  subdirectories:
    high_quality: "high_quality"
    edge_cases: "edge_cases"
    malicious: "malicious"
    templates: "templates_used"
  report_filename: "generation_report.json"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "cv_generator.log"
  max_file_size_mb: 10
  backup_count: 5

# API Configuration (optional)
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]
  max_upload_size_mb: 100
