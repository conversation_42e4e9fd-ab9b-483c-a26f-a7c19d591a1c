# ATS CV Generator - API Reference

Complete reference for the REST API endpoints and CLI interface.

## REST API

### Base URL
```
http://localhost:8000
```

### Authentication
No authentication required for local deployment. For production, implement appropriate security measures.

### Content Types
- Request: `multipart/form-data` for file uploads, `application/json` for data
- Response: `application/json`

---

## Endpoints

### GET /
Root endpoint with basic service information.

**Response:**
```json
{
  "message": "ATS CV Generator API",
  "version": "1.0.0",
  "docs": "/docs"
}
```

### GET /health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "ats-cv-generator"
}
```

### POST /generate
Generate resumes based on templates and parameters.

**Parameters:**
- `job_description` (form field, required): Job description text (100-3000 characters)
- `total_count` (form field, required): Total number of resumes to generate
- `distribution` (form field, required): JSON string with distribution percentages
- `enable_pdf_conversion` (form field, optional): Boolean, default true
- `templates` (files, required): JPEG template files (1-50 files, max 10MB each)

**Example Request:**
```bash
curl -X POST "http://localhost:8000/generate" \
  -F "job_description=Software Engineer position requiring Python and JavaScript experience" \
  -F "total_count=100" \
  -F "distribution={\"high_quality\":70,\"edge_cases\":20,\"malicious\":10}" \
  -F "enable_pdf_conversion=true" \
  -F "templates=@template1.jpg" \
  -F "templates=@template2.jpg"
```

**Response:**
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "accepted",
  "message": "Generation request accepted and queued for processing",
  "estimated_completion_time": 180.5
}
```

### GET /status/{job_id}
Get status of a generation job.

**Parameters:**
- `job_id` (path): Job ID returned from /generate

**Response:**
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "running",
  "progress": 45.2,
  "total_resumes": 100,
  "completed_resumes": 45,
  "failed_resumes": 0,
  "created_at": "2024-01-15T10:30:00Z",
  "started_at": "2024-01-15T10:30:05Z",
  "completed_at": null,
  "output_directory": "/path/to/output",
  "error_message": null
}
```

**Status Values:**
- `pending`: Job queued but not started
- `running`: Job in progress
- `completed`: Job finished successfully
- `failed`: Job failed with errors

### GET /jobs
List all active jobs.

**Response:**
```json
{
  "jobs": [
    {
      "job_id": "abc123-def456-ghi789",
      "status": "running",
      "progress": 45.2,
      "total_resumes": 100,
      "completed_resumes": 45,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1
}
```

### GET /download/{job_id}
Download results as ZIP file.

**Parameters:**
- `job_id` (path): Job ID of completed job

**Response:**
- Content-Type: `application/zip`
- File download with name: `cv_generation_{job_id}.zip`

**Example:**
```bash
curl -O "http://localhost:8000/download/abc123-def456-ghi789"
```

### GET /statistics
Get processing statistics.

**Response:**
```json
{
  "total_jobs": 15,
  "completed_jobs": 12,
  "failed_jobs": 1,
  "total_resumes_generated": 1200,
  "total_processing_time": 3600.5,
  "average_processing_time": 300.04,
  "average_resumes_per_job": 100,
  "active_jobs": 2
}
```

### DELETE /jobs/{job_id}
Cancel a running job.

**Parameters:**
- `job_id` (path): Job ID to cancel

**Response:**
```json
{
  "message": "Job cancellation requested",
  "job_id": "abc123-def456-ghi789"
}
```

### POST /cleanup
Clean up old completed jobs.

**Parameters:**
- `max_age_hours` (query, optional): Maximum age in hours, default 24

**Response:**
```json
{
  "message": "Cleaned up 5 old jobs"
}
```

---

## CLI Interface

### Basic Usage
```bash
python generate_resumes.py [OPTIONS]
```

### Required Options
- `--templates, -t`: Directory containing JPEG template files
- `--count, -c`: Total number of resumes to generate
- `--distribution, -d`: Distribution ratios as "high,edge,malicious"
- `--job-desc, -j`: Job description for content tailoring

### Optional Options
- `--output, -o`: Custom output directory
- `--config-file`: Custom configuration file path
- `--no-pdf`: Skip PDF conversion (HTML only)
- `--verbose, -v`: Enable verbose logging
- `--dry-run`: Validate parameters without generating

### Examples

#### Basic Generation
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 100 \
  --distribution "70,20,10" \
  --job-desc "Software Engineer position requiring Python, JavaScript, and database experience"
```

#### Custom Output Directory
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 500 \
  --distribution "80,15,5" \
  --job-desc "Data Scientist role with machine learning focus" \
  --output ./custom_output/
```

#### HTML Only (No PDF)
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 200 \
  --distribution "75,15,10" \
  --job-desc "Product Manager position" \
  --no-pdf
```

#### Dry Run Validation
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 50 \
  --distribution "60,25,15" \
  --job-desc "Marketing Manager role" \
  --dry-run
```

#### Verbose Logging
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 100 \
  --distribution "70,20,10" \
  --job-desc "DevOps Engineer position" \
  --verbose
```

---

## Error Codes

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (job/resource not found)
- `500`: Internal Server Error

### Common Error Messages

#### Validation Errors
```json
{
  "detail": "Distribution percentages must sum to 100, got 95"
}
```

```json
{
  "detail": "Job description too short: 50 < 100"
}
```

```json
{
  "detail": "Too many templates: 55 > 50"
}
```

#### File Errors
```json
{
  "detail": "Invalid file type: document.pdf. Only JPEG files are supported."
}
```

```json
{
  "detail": "File too large: template.jpg > 10MB"
}
```

#### Processing Errors
```json
{
  "detail": "Job not found"
}
```

```json
{
  "detail": "Job not completed"
}
```

---

## Rate Limits

### API Rate Limits
- **Generation requests**: 10 per minute per IP
- **Status checks**: 100 per minute per IP
- **File downloads**: 5 per minute per IP

### Gemini API Limits
- **Requests**: 60 per minute (configurable)
- **Automatic retry**: 3 attempts with exponential backoff

---

## Response Schemas

### GenerationRequest
```json
{
  "job_description": "string (100-3000 chars)",
  "total_count": "integer (1-10000)",
  "distribution": {
    "high_quality": "integer (0-100)",
    "edge_cases": "integer (0-100)", 
    "malicious": "integer (0-100)"
  },
  "enable_pdf_conversion": "boolean"
}
```

### JobStatus
```json
{
  "job_id": "string",
  "status": "pending|running|completed|failed",
  "progress": "float (0-100)",
  "total_resumes": "integer",
  "completed_resumes": "integer",
  "failed_resumes": "integer",
  "created_at": "ISO datetime",
  "started_at": "ISO datetime|null",
  "completed_at": "ISO datetime|null",
  "output_directory": "string|null",
  "error_message": "string|null"
}
```

### ValidationResult
```json
{
  "is_valid": "boolean",
  "errors": ["string"],
  "warnings": ["string"]
}
```

---

## SDK Examples

### Python SDK Example
```python
import requests
import json

# Start generation
response = requests.post('http://localhost:8000/generate', 
    data={
        'job_description': 'Software Engineer position...',
        'total_count': 100,
        'distribution': json.dumps({"high_quality": 70, "edge_cases": 20, "malicious": 10}),
        'enable_pdf_conversion': True
    },
    files=[
        ('templates', open('template1.jpg', 'rb')),
        ('templates', open('template2.jpg', 'rb'))
    ]
)

job_data = response.json()
job_id = job_data['job_id']

# Check status
status_response = requests.get(f'http://localhost:8000/status/{job_id}')
status = status_response.json()

# Download results when complete
if status['status'] == 'completed':
    download_response = requests.get(f'http://localhost:8000/download/{job_id}')
    with open(f'results_{job_id}.zip', 'wb') as f:
        f.write(download_response.content)
```

### JavaScript SDK Example
```javascript
const formData = new FormData();
formData.append('job_description', 'Software Engineer position...');
formData.append('total_count', '100');
formData.append('distribution', JSON.stringify({
    "high_quality": 70,
    "edge_cases": 20, 
    "malicious": 10
}));
formData.append('templates', templateFile1);
formData.append('templates', templateFile2);

// Start generation
const response = await fetch('http://localhost:8000/generate', {
    method: 'POST',
    body: formData
});

const jobData = await response.json();
const jobId = jobData.job_id;

// Poll for completion
const checkStatus = async () => {
    const statusResponse = await fetch(`http://localhost:8000/status/${jobId}`);
    const status = await statusResponse.json();
    
    if (status.status === 'completed') {
        // Download results
        window.location.href = `http://localhost:8000/download/${jobId}`;
    } else if (status.status === 'running') {
        setTimeout(checkStatus, 5000); // Check again in 5 seconds
    }
};

checkStatus();
```
