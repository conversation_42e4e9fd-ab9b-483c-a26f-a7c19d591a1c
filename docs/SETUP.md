# ATS CV Generator - Setup Guide

This guide will help you set up and configure the ATS CV Generator system.

## Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Node.js**: 14 or higher (for PDF conversion)
- **Memory**: 2GB+ RAM recommended
- **Storage**: 1GB+ free disk space
- **OS**: Linux, macOS, or Windows

### API Requirements
- **Google Gemini API Key**: Required for AI-powered resume generation
- **Internet Connection**: Required for API calls

## Installation

### 1. Clone or Download the Project
```bash
git clone <repository-url>
cd hiring-cv-generator
```

### 2. Create Virtual Environment (Recommended)
```bash
python -m venv venv

# On Linux/macOS:
source venv/bin/activate

# On Windows:
venv\Scripts\activate
```

### 3. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install Node.js Dependencies (for PDF conversion)
```bash
# Install Puppeteer globally (optional but recommended)
npm install -g puppeteer

# Or install locally
npm install puppeteer
```

### 5. Install System Dependencies

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install -y \
    chromium-browser \
    wkhtmltopdf \
    fonts-liberation \
    fonts-dejavu-core
```

#### CentOS/RHEL:
```bash
sudo yum install -y \
    chromium \
    wkhtmltopdf \
    liberation-fonts \
    dejavu-sans-fonts
```

#### macOS:
```bash
brew install --cask chromium
brew install wkhtmltopdf
```

#### Windows:
- Download and install Chrome or Chromium
- Download wkhtmltopdf from: https://wkhtmltopdf.org/downloads.html

## Configuration

### 1. Get Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for configuration

### 2. Configure Environment Variables
Create a `.env` file in the project root:
```bash
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional
CV_GENERATOR_LOG_LEVEL=INFO
CV_GENERATOR_OUTPUT_DIR=./generated_resumes
```

### 3. Update Configuration File
Edit `config.yaml` to customize settings:

```yaml
# Essential settings to review
gemini:
  api_key: ""  # Will use GEMINI_API_KEY environment variable

processing:
  max_concurrent_threads: 4  # Adjust based on your system

output:
  base_directory: "./generated_resumes"  # Change if needed

logging:
  level: "INFO"  # Change to "DEBUG" for verbose output
```

## Verification

### 1. Test Installation
```bash
python -c "import src.config; print('Configuration loaded successfully')"
```

### 2. Test API Connection
```bash
python -c "
import asyncio
from src.gemini_client import GeminiClient
client = GeminiClient()
result = asyncio.run(client.test_connection())
print('API connection:', 'SUCCESS' if result else 'FAILED')
"
```

### 3. Test CLI
```bash
python generate_resumes.py --help
```

### 4. Test PDF Conversion
```bash
python -c "
import asyncio
from src.pdf_converter import PDFConverter
print('PDF converter available:', 'YES' if PDFConverter else 'NO')
"
```

## Quick Start

### 1. Prepare Templates
Create a `templates/` directory and add JPEG template files:
```bash
mkdir templates
# Copy your JPEG template files to this directory
```

### 2. Run First Generation
```bash
python generate_resumes.py \
  --templates ./templates/ \
  --count 10 \
  --distribution "70,20,10" \
  --job-desc "Software Engineer position requiring Python, JavaScript, and database experience. 3+ years experience preferred."
```

### 3. Check Output
Results will be in `./generated_resumes_[timestamp]/`:
- `high_quality/` - Professional resumes
- `edge_cases/` - Non-standard resumes  
- `malicious/` - Adversarial resumes
- `templates_used/` - Copy of templates
- `generation_report.json` - Detailed statistics

## API Server Setup (Optional)

### 1. Start API Server
```bash
python -m src.api.server
```

### 2. Test API
```bash
curl http://localhost:8000/health
```

### 3. View API Documentation
Open http://localhost:8000/docs in your browser

## Troubleshooting

### Common Issues

#### "Gemini API key not found"
- Set the `GEMINI_API_KEY` environment variable
- Or update `config.yaml` with your API key

#### "No module named 'src'"
- Make sure you're in the project root directory
- Check that `src/__init__.py` exists

#### "PDF conversion failed"
- Install Chrome/Chromium browser
- Install wkhtmltopdf
- Check that browsers are in system PATH

#### "Permission denied" errors
- Check file/directory permissions
- Run with appropriate user privileges
- Ensure output directory is writable

#### "Out of memory" errors
- Reduce `max_concurrent_threads` in config
- Reduce batch size for large generations
- Increase system memory or use swap

### Performance Optimization

#### For Large Generations (1000+ resumes):
1. Increase concurrent threads (if system allows):
   ```yaml
   processing:
     max_concurrent_threads: 8
   ```

2. Use SSD storage for output directory

3. Monitor system resources:
   ```bash
   htop  # or top on older systems
   ```

#### For Limited Resources:
1. Reduce concurrent threads:
   ```yaml
   processing:
     max_concurrent_threads: 2
   ```

2. Disable PDF conversion for faster processing:
   ```bash
   python generate_resumes.py --no-pdf ...
   ```

### Getting Help

1. **Check Logs**: Look at `cv_generator.log` for detailed error messages
2. **Enable Debug Mode**: Set log level to DEBUG in config
3. **Validate Configuration**: Use the validation tools
4. **Check System Resources**: Ensure adequate memory and disk space

### Advanced Configuration

#### Custom Output Structure
```yaml
output:
  base_directory: "/custom/path"
  subdirectories:
    high_quality: "professional"
    edge_cases: "unusual"
    malicious: "adversarial"
```

#### API Customization
```yaml
api:
  host: "0.0.0.0"
  port: 8080
  cors_origins: ["http://localhost:3000"]
```

#### Logging Customization
```yaml
logging:
  level: "DEBUG"
  file: "/var/log/cv_generator.log"
  max_file_size_mb: 50
```

## Next Steps

1. **Read the API Reference**: `docs/API.md`
2. **Review Examples**: `docs/EXAMPLES.md`
3. **Understand Template Creation**: `docs/TEMPLATES.md`
4. **Learn About Testing**: `docs/TESTING.md`
