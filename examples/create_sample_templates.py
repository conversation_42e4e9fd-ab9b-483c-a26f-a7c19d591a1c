#!/usr/bin/env python3
"""
Create sample template images for testing and demonstration.

This script generates various types of resume template images that can be used
for testing the ATS CV Generator system.
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import argparse


def create_template_image(width, height, template_type, output_path):
    """Create a template image based on type."""
    
    # Color schemes for different template types
    color_schemes = {
        'simple': {
            'bg': 'white',
            'header': '#f0f0f0',
            'text': 'black',
            'accent': '#333333'
        },
        'corporate': {
            'bg': 'white',
            'header': '#2c3e50',
            'text': 'black',
            'accent': '#3498db'
        },
        'creative': {
            'bg': '#fafafa',
            'header': '#e74c3c',
            'text': '#2c3e50',
            'accent': '#f39c12'
        },
        'minimalist': {
            'bg': 'white',
            'header': 'white',
            'text': '#333333',
            'accent': '#666666'
        },
        'modern': {
            'bg': '#f8f9fa',
            'header': '#343a40',
            'text': '#212529',
            'accent': '#007bff'
        }
    }
    
    colors = color_schemes.get(template_type, color_schemes['simple'])
    
    # Create image
    img = Image.new('RGB', (width, height), color=colors['bg'])
    draw = ImageDraw.Draw(img)
    
    # Try to load a font
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        header_font = ImageFont.truetype("arial.ttf", 18)
        text_font = ImageFont.truetype("arial.ttf", 14)
    except:
        try:
            title_font = ImageFont.load_default()
            header_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
        except:
            title_font = header_font = text_font = None
    
    margin = 50
    y_pos = margin
    
    # Header section
    if template_type == 'corporate':
        # Full-width header
        draw.rectangle([0, 0, width, 120], fill=colors['header'])
        draw.text((margin, 30), "FULL NAME", fill='white', font=title_font)
        draw.text((margin, 60), "Professional Title", fill='white', font=header_font)
        draw.text((margin, 85), "<EMAIL> | (555) 123-4567", fill='white', font=text_font)
        y_pos = 150
    elif template_type == 'creative':
        # Colored header with accent
        draw.rectangle([0, 0, width, 100], fill=colors['header'])
        draw.rectangle([0, 100, width, 110], fill=colors['accent'])
        draw.text((margin, 25), "CREATIVE NAME", fill='white', font=title_font)
        draw.text((margin, 55), "Designer & Developer", fill='white', font=header_font)
        y_pos = 140
    elif template_type == 'minimalist':
        # Simple text header
        draw.text((margin, y_pos), "Minimalist Name", fill=colors['text'], font=title_font)
        y_pos += 40
        draw.text((margin, y_pos), "Professional Role", fill=colors['accent'], font=header_font)
        y_pos += 30
        draw.line([margin, y_pos, width-margin, y_pos], fill=colors['accent'], width=1)
        y_pos += 30
    else:
        # Standard header
        draw.rectangle([margin, y_pos, width-margin, y_pos+80], outline=colors['accent'], width=2)
        draw.text((margin+10, y_pos+10), "PROFESSIONAL NAME", fill=colors['text'], font=title_font)
        draw.text((margin+10, y_pos+40), "Job Title", fill=colors['text'], font=header_font)
        y_pos += 100
    
    # Contact section
    if template_type != 'corporate':  # Corporate already has contact in header
        draw.text((margin, y_pos), "Contact Information", fill=colors['accent'], font=header_font)
        y_pos += 30
        draw.text((margin, y_pos), "📧 <EMAIL>", fill=colors['text'], font=text_font)
        y_pos += 25
        draw.text((margin, y_pos), "📱 (555) 123-4567", fill=colors['text'], font=text_font)
        y_pos += 40
    
    # Experience section
    draw.text((margin, y_pos), "PROFESSIONAL EXPERIENCE", fill=colors['accent'], font=header_font)
    if template_type == 'minimalist':
        draw.line([margin, y_pos+25, width-margin, y_pos+25], fill=colors['accent'], width=1)
    y_pos += 40
    
    # Job entries
    for i in range(2):
        draw.text((margin, y_pos), f"Senior Position {i+1}", fill=colors['text'], font=header_font)
        y_pos += 25
        draw.text((margin, y_pos), f"Company Name {i+1} | 2020-Present", fill=colors['accent'], font=text_font)
        y_pos += 25
        
        # Bullet points
        for j in range(3):
            draw.text((margin+20, y_pos), f"• Achievement or responsibility {j+1}", fill=colors['text'], font=text_font)
            y_pos += 20
        y_pos += 15
    
    # Education section
    draw.text((margin, y_pos), "EDUCATION", fill=colors['accent'], font=header_font)
    if template_type == 'minimalist':
        draw.line([margin, y_pos+25, width-margin, y_pos+25], fill=colors['accent'], width=1)
    y_pos += 40
    
    draw.text((margin, y_pos), "Bachelor of Science in Computer Science", fill=colors['text'], font=header_font)
    y_pos += 25
    draw.text((margin, y_pos), "University Name | 2016-2020", fill=colors['accent'], font=text_font)
    y_pos += 40
    
    # Skills section
    draw.text((margin, y_pos), "TECHNICAL SKILLS", fill=colors['accent'], font=header_font)
    if template_type == 'minimalist':
        draw.line([margin, y_pos+25, width-margin, y_pos+25], fill=colors['accent'], width=1)
    y_pos += 40
    
    skills = ["Python, JavaScript, React", "SQL, MongoDB, PostgreSQL", "Git, Docker, AWS", "Agile, Scrum, CI/CD"]
    for skill in skills:
        draw.text((margin, y_pos), f"• {skill}", fill=colors['text'], font=text_font)
        y_pos += 25
    
    # Add template type watermark
    if template_type != 'minimalist':
        draw.text((width-200, height-30), f"{template_type.title()} Template", 
                 fill=colors['accent'], font=text_font)
    
    # Save image
    img.save(output_path, 'JPEG', quality=90)
    print(f"Created {template_type} template: {output_path}")


def main():
    """Create sample templates."""
    parser = argparse.ArgumentParser(description="Create sample resume templates")
    parser.add_argument("--output-dir", "-o", default="templates", 
                       help="Output directory for templates")
    parser.add_argument("--width", "-w", type=int, default=800, 
                       help="Template width in pixels")
    parser.add_argument("--height", type=int, default=1000,
                       help="Template height in pixels")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Template types to create
    template_types = ['simple', 'corporate', 'creative', 'minimalist', 'modern']
    
    print(f"Creating {len(template_types)} sample templates...")
    print(f"Output directory: {output_dir.absolute()}")
    print(f"Dimensions: {args.width}x{args.height}")
    print()
    
    for template_type in template_types:
        output_path = output_dir / f"{template_type}_template.jpg"
        create_template_image(args.width, args.height, template_type, output_path)
    
    print()
    print("Sample templates created successfully!")
    print(f"You can now use these templates with:")
    print(f"python generate_resumes.py --templates {output_dir} --count 10 --distribution '70,20,10' --job-desc 'Your job description here'")


if __name__ == "__main__":
    main()
