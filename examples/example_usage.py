#!/usr/bin/env python3
"""
Example usage of the ATS CV Generator system.

This script demonstrates various ways to use the CV generator programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models import GenerationRequest
from src.batch_processor import BatchProcessor
from src.gemini_client import GeminiClient
from src.template_processor import TemplateProcessor
from src.validation import InputValidator


async def example_basic_generation():
    """Example: Basic resume generation."""
    print("=== Basic Resume Generation Example ===")
    
    # Sample job description
    job_description = """
    Software Engineer Position
    
    We are seeking a skilled Software Engineer to join our development team. 
    The ideal candidate will have experience with Python, JavaScript, and modern web frameworks.
    
    Requirements:
    - 3+ years of software development experience
    - Proficiency in Python and JavaScript
    - Experience with React, Node.js, or similar frameworks
    - Knowledge of database systems (SQL and NoSQL)
    - Understanding of version control systems (Git)
    - Strong problem-solving and communication skills
    
    Responsibilities:
    - Develop and maintain web applications
    - Collaborate with cross-functional teams
    - Write clean, maintainable code
    - Participate in code reviews
    - Troubleshoot and debug applications
    """
    
    # Check if templates exist
    templates_dir = Path("templates")
    if not templates_dir.exists():
        print("Templates directory not found. Creating sample templates...")
        # Create sample templates
        import subprocess
        subprocess.run([sys.executable, "examples/create_sample_templates.py"])
    
    # Get template files
    template_files = list(templates_dir.glob("*.jpg"))
    if not template_files:
        print("No template files found. Please run create_sample_templates.py first.")
        return
    
    template_paths = [str(f) for f in template_files[:3]]  # Use first 3 templates
    
    # Create generation request
    request = GenerationRequest(
        job_description=job_description,
        total_count=10,
        distribution={"high_quality": 70, "edge_cases": 20, "malicious": 10},
        template_paths=template_paths
    )
    
    # Validate request
    validator = InputValidator()
    validation_result = validator.validate_generation_request(request)
    
    if not validation_result.is_valid:
        print("Validation failed:")
        for error in validation_result.errors:
            print(f"  - {error}")
        return
    
    if validation_result.warnings:
        print("Validation warnings:")
        for warning in validation_result.warnings:
            print(f"  - {warning}")
    
    # Process generation
    processor = BatchProcessor()
    
    # Add progress callback
    def progress_callback(job_id, progress, details):
        status = details.get("status", "unknown")
        phase = details.get("phase", "")
        print(f"Job {job_id[:8]}: {progress:.1f}% - {status} ({phase})")
    
    processor.add_progress_callback(progress_callback)
    
    try:
        print(f"Starting generation of {request.total_count} resumes...")
        job = await processor.process_generation_request(request, enable_pdf_conversion=False)
        
        if job.status == "completed":
            print(f"\nGeneration completed successfully!")
            print(f"Output directory: {job.output_directory}")
            print(f"Completed resumes: {job.completed_resumes}")
            print(f"Failed resumes: {job.failed_resumes}")
        else:
            print(f"Generation failed: {job.error_message}")
            
    except Exception as e:
        print(f"Error during generation: {e}")


async def example_template_analysis():
    """Example: Analyze template files."""
    print("\n=== Template Analysis Example ===")
    
    templates_dir = Path("templates")
    if not templates_dir.exists():
        print("Templates directory not found.")
        return
    
    template_files = list(templates_dir.glob("*.jpg"))
    if not template_files:
        print("No template files found.")
        return
    
    processor = TemplateProcessor()
    
    # Load and analyze templates
    template_paths = [str(f) for f in template_files]
    templates = processor.load_templates(template_paths)
    
    print(f"Loaded {len(templates)} templates:")
    for template in templates:
        print(f"  - {Path(template.path).name}")
        print(f"    Category: {template.category}")
        print(f"    Size: {template.file_size_mb:.2f} MB")
        print(f"    Dimensions: {template.dimensions}")
        print()
    
    # Get statistics
    stats = processor.get_template_statistics()
    print("Template Statistics:")
    print(f"  Total templates: {stats['total_templates']}")
    print(f"  Categories: {stats['categories']}")
    print(f"  File sizes: {stats.get('file_sizes', {})}")


async def example_gemini_client():
    """Example: Test Gemini client connection."""
    print("\n=== Gemini Client Example ===")
    
    try:
        client = GeminiClient()
        
        # Test connection
        print("Testing Gemini API connection...")
        is_connected = await client.test_connection()
        
        if is_connected:
            print("✓ Gemini API connection successful")
            
            # Test template info
            templates_dir = Path("templates")
            template_files = list(templates_dir.glob("*.jpg"))
            
            if template_files:
                template_path = str(template_files[0])
                print(f"Getting info for template: {Path(template_path).name}")
                
                template_info = client.get_template_info(template_path)
                print(f"  Dimensions: {template_info['dimensions']}")
                print(f"  File size: {template_info['file_size_mb']:.2f} MB")
                print(f"  Hash: {template_info['hash'][:16]}...")
            
        else:
            print("✗ Gemini API connection failed")
            print("Please check your API key configuration")
            
    except Exception as e:
        print(f"Error testing Gemini client: {e}")


async def example_validation():
    """Example: Input validation."""
    print("\n=== Input Validation Example ===")
    
    validator = InputValidator()
    
    # Test job description validation
    print("Testing job description validation:")
    
    test_cases = [
        ("Valid description", "Software Engineer position requiring Python and JavaScript experience. " * 5),
        ("Too short", "Short job"),
        ("Too long", "A" * 4000),
        ("Empty", ""),
    ]
    
    for name, job_desc in test_cases:
        result = validator._validate_job_description(job_desc)
        status = "✓ Valid" if result.is_valid else "✗ Invalid"
        print(f"  {name}: {status}")
        if result.errors:
            for error in result.errors[:1]:  # Show first error
                print(f"    Error: {error}")
    
    # Test distribution validation
    print("\nTesting distribution validation:")
    
    distribution_cases = [
        ("Valid distribution", {"high_quality": 70, "edge_cases": 20, "malicious": 10}),
        ("Invalid sum", {"high_quality": 70, "edge_cases": 20, "malicious": 5}),
        ("Missing keys", {"high_quality": 70, "edge_cases": 30}),
        ("Negative values", {"high_quality": -10, "edge_cases": 60, "malicious": 50}),
    ]
    
    for name, distribution in distribution_cases:
        result = validator._validate_distribution(distribution)
        status = "✓ Valid" if result.is_valid else "✗ Invalid"
        print(f"  {name}: {status}")
        if result.errors:
            for error in result.errors[:1]:  # Show first error
                print(f"    Error: {error}")


async def example_custom_configuration():
    """Example: Custom configuration usage."""
    print("\n=== Custom Configuration Example ===")
    
    from src.config import Config, GeminiConfig, GenerationConfig
    
    # Create custom configuration
    custom_config = Config(
        gemini=GeminiConfig(
            api_key="custom_key",
            max_retries=5,
            rate_limit=30
        ),
        generation=GenerationConfig(
            max_total_count=500,
            default_distribution={"high_quality": 80, "edge_cases": 15, "malicious": 5}
        )
    )
    
    print("Custom configuration created:")
    print(f"  Max retries: {custom_config.gemini.max_retries}")
    print(f"  Rate limit: {custom_config.gemini.rate_limit}")
    print(f"  Max total count: {custom_config.generation.max_total_count}")
    print(f"  Default distribution: {custom_config.generation.default_distribution}")


async def main():
    """Run all examples."""
    print("ATS CV Generator - Usage Examples")
    print("=" * 50)
    
    # Run examples
    await example_basic_generation()
    await example_template_analysis()
    await example_gemini_client()
    await example_validation()
    await example_custom_configuration()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nNext steps:")
    print("1. Set up your Gemini API key: export GEMINI_API_KEY='your-key'")
    print("2. Create templates: python examples/create_sample_templates.py")
    print("3. Generate resumes: python generate_resumes.py --help")


if __name__ == "__main__":
    asyncio.run(main())
