[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests that may take longer to run
    api: API-related tests
    pdf: PDF conversion tests
    gemini: Tests requiring Gemini API
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
