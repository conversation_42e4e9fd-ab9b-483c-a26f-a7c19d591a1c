{"cells": [{"cell_type": "code", "execution_count": 9, "id": "09b759a5", "metadata": {}, "outputs": [], "source": ["# vars\n", "job_description = '''Software Engineer Position\n", "\n", "We are seeking a skilled Software Engineer to join our development team. The ideal candidate will have experience with Python, JavaScript, and modern web frameworks.\n", "\n", "Requirements:\n", "- 3+ years of software development experience\n", "- Proficiency in Python and JavaScript\n", "- Experience with React, Node.js, or similar frameworks\n", "- Knowledge of database systems (SQL and NoSQL)\n", "- Understanding of version control systems (Git)\n", "- Strong problem-solving and communication skills\n", "\n", "Responsibilities:\n", "- Develop and maintain web applications\n", "- Collaborate with cross-functional teams\n", "- Write clean, maintainable code\n", "- Participate in code reviews\n", "- Troubleshoot and debug applications'''\n", "\n", "COUNT = 1\n", "DISTRIBUTION = \"70,20,10\"\n", "LAYOUTS = \"modern_sidebar\"\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4e8e859e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[36mATS CV Generator\u001b[0m\n", "==================================================\n", "Validating parameters...\n", "\u001b[31mError: No JPEG template files found in: templates\u001b[0m\n", "\u001b[0m"]}], "source": ["!python generate_resumes.py --templates templates --count {COUNT} --distribution '{DISTRIBUTION}' --job-desc \"{JOB_DESCRIPTION}\""]}, {"cell_type": "code", "execution_count": 10, "id": "8fb7ae8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[36mATS CV Generator\u001b[0m\n", "==================================================\n", "Validating parameters...\n", "Using 1 layout descriptions\n", "\n", "\u001b[33mGeneration Summary:\u001b[0m\n", "Total resumes: 1\n", "Distribution: {'high_quality': 70, 'edge_cases': 20, 'malicious': 10}\n", "Layouts: modern_sidebar\n", "Job description: 670 characters\n", "PDF conversion: Enabled\n", "\n", "\u001b[36mStarting generation...\u001b[0m\n", "Converting to PDF...:  80%|▊| 80/100 [00:12<00:03,  5.46%/s, completed=1, failedPyppeteer conversion failed: Page.setContent() got an unexpected keyword argument 'waitUntil'\n", "Failed to convert resume 49861040-1aa8-4581-991d-b89b3aad06ab: [\"Pyppeteer conversion failed: Page.setContent() got an unexpected keyword argument 'waitUntil'\"]\n", "Processing (): 100%|████| 100/100 [00:13<00:00,  7.57%/s, completed=1, failed=0]\n", "\n", "\u001b[32m✓ Generation completed successfully!\u001b[0m\n", "Output directory: generated_resumes/generated_resumes_20250915_133452_573f77b0\n", "Completed resumes: 1\n", "\n", "\u001b[32mGeneration Statistics:\u001b[0m\n", "Successful resumes: 1\n", "Failed resumes: 0\n", "Processing time: 13.2s\n", "Generation rate: 4.5 resumes/minute\n", "\u001b[0m"]}], "source": ["!python generate_resumes.py --layouts {LAYOUTS} --count {COUNT} --distribution '{DISTRIBUTION}' --job-desc \"{JOB_DESCRIPTION}\"\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}