"""REST API server for ATS CV Generator."""

import asyncio
import tempfile
import shutil
import zipfile
from typing import List, Dict, Any, Optional
from pathlib import Path
import json
import logging

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, ValidationError
import uvicorn

from ..config import config
from ..models import GenerationRequest, GenerationJob, ResumeType
from ..batch_processor import BatchProcessor
from ..utils import setup_logging, validate_distribution_percentages


# Setup logging
logger = setup_logging(config)

# Initialize FastAPI app
app = FastAPI(
    title="ATS CV Generator API",
    description="Generate comprehensive CV/resume datasets for ATS testing",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.api.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global batch processor
batch_processor = BatchProcessor()


class GenerationRequestAPI(BaseModel):
    """API model for generation request."""
    job_description: str
    total_count: int
    distribution: Dict[str, int]
    enable_pdf_conversion: bool = True


class JobStatusResponse(BaseModel):
    """API response for job status."""
    job_id: str
    status: str
    progress: float
    total_resumes: int
    completed_resumes: int
    failed_resumes: int
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    output_directory: Optional[str] = None
    error_message: Optional[str] = None


class GenerationResponse(BaseModel):
    """API response for generation request."""
    job_id: str
    status: str
    message: str
    estimated_completion_time: Optional[float] = None


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "ATS CV Generator API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "ats-cv-generator"}


@app.post("/generate", response_model=GenerationResponse)
async def generate_resumes(
    background_tasks: BackgroundTasks,
    job_description: str = Form(...),
    total_count: int = Form(...),
    distribution: str = Form(...),
    enable_pdf_conversion: bool = Form(True),
    templates: List[UploadFile] = File(...)
):
    """
    Generate resumes based on uploaded templates and parameters.
    
    - **job_description**: Job description for content tailoring (100-3000 chars)
    - **total_count**: Total number of resumes to generate
    - **distribution**: Distribution as JSON string, e.g., '{"high_quality":70,"edge_cases":20,"malicious":10}'
    - **enable_pdf_conversion**: Whether to convert HTML to PDF
    - **templates**: JPEG template files (1-50 files, max 10MB each)
    """
    try:
        # Validate templates
        if not templates:
            raise HTTPException(status_code=400, detail="No template files provided")
        
        if len(templates) > config.templates.max_count:
            raise HTTPException(
                status_code=400, 
                detail=f"Too many templates: {len(templates)} > {config.templates.max_count}"
            )
        
        # Validate and parse distribution
        try:
            distribution_dict = json.loads(distribution)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid distribution JSON format")
        
        is_valid, error_msg = validate_distribution_percentages(distribution_dict)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"Invalid distribution: {error_msg}")
        
        # Validate job description
        if len(job_description) < config.generation.min_job_description_length:
            raise HTTPException(
                status_code=400, 
                detail=f"Job description too short: {len(job_description)} < {config.generation.min_job_description_length}"
            )
        
        if len(job_description) > config.generation.max_job_description_length:
            raise HTTPException(
                status_code=400, 
                detail=f"Job description too long: {len(job_description)} > {config.generation.max_job_description_length}"
            )
        
        # Validate total count
        if total_count > config.generation.max_total_count:
            raise HTTPException(
                status_code=400, 
                detail=f"Total count too high: {total_count} > {config.generation.max_total_count}"
            )
        
        # Save uploaded templates to temporary directory
        temp_dir = Path(tempfile.mkdtemp(prefix="cv_generator_templates_"))
        template_paths = []
        
        try:
            for template in templates:
                # Validate file type
                if not template.filename.lower().endswith(('.jpg', '.jpeg')):
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Invalid file type: {template.filename}. Only JPEG files are supported."
                    )
                
                # Check file size
                content = await template.read()
                if len(content) > config.templates.max_file_size_mb * 1024 * 1024:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"File too large: {template.filename} > {config.templates.max_file_size_mb}MB"
                    )
                
                # Save file
                template_path = temp_dir / template.filename
                with open(template_path, 'wb') as f:
                    f.write(content)
                
                template_paths.append(str(template_path))
            
            # Create generation request
            request = GenerationRequest(
                job_description=job_description,
                total_count=total_count,
                distribution=distribution_dict,
                template_paths=template_paths
            )
            
            # Estimate completion time
            estimated_time = batch_processor.estimate_completion_time(request)
            
            # Start background processing
            background_tasks.add_task(
                process_generation_background,
                request,
                enable_pdf_conversion,
                temp_dir
            )
            
            return GenerationResponse(
                job_id="pending",  # Will be updated in background task
                status="accepted",
                message="Generation request accepted and queued for processing",
                estimated_completion_time=estimated_time
            )
            
        except Exception as e:
            # Clean up temporary directory on error
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)
            raise
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in generate endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def process_generation_background(
    request: GenerationRequest, 
    enable_pdf: bool, 
    temp_dir: Path
):
    """Background task for processing generation request."""
    try:
        job = await batch_processor.process_generation_request(request, enable_pdf)
        logger.info(f"Background generation completed: {job.job_id}")
    except Exception as e:
        logger.error(f"Background generation failed: {str(e)}")
    finally:
        # Clean up temporary directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)


@app.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get status of a generation job."""
    job = batch_processor.get_job_status(job_id)
    
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job.job_id,
        status=job.status,
        progress=job.progress,
        total_resumes=job.total_resumes,
        completed_resumes=job.completed_resumes,
        failed_resumes=job.failed_resumes,
        created_at=job.created_at.isoformat(),
        started_at=job.started_at.isoformat() if job.started_at else None,
        completed_at=job.completed_at.isoformat() if job.completed_at else None,
        output_directory=job.output_directory,
        error_message=job.error_message
    )


@app.get("/jobs")
async def list_jobs():
    """List all active jobs."""
    jobs = batch_processor.list_active_jobs()
    
    return {
        "jobs": [
            {
                "job_id": job.job_id,
                "status": job.status,
                "progress": job.progress,
                "total_resumes": job.total_resumes,
                "completed_resumes": job.completed_resumes,
                "created_at": job.created_at.isoformat()
            }
            for job in jobs
        ],
        "total": len(jobs)
    }


@app.get("/download/{job_id}")
async def download_results(job_id: str):
    """Download results as ZIP file."""
    job = batch_processor.get_job_status(job_id)
    
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job.status != "completed":
        raise HTTPException(status_code=400, detail="Job not completed")
    
    if not job.output_directory or not Path(job.output_directory).exists():
        raise HTTPException(status_code=404, detail="Output directory not found")
    
    # Create ZIP file
    zip_path = Path(tempfile.mktemp(suffix=".zip"))
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            output_dir = Path(job.output_directory)
            for file_path in output_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(output_dir)
                    zipf.write(file_path, arcname)
        
        return FileResponse(
            path=str(zip_path),
            filename=f"cv_generation_{job_id[:8]}.zip",
            media_type="application/zip"
        )
        
    except Exception as e:
        if zip_path.exists():
            zip_path.unlink()
        raise HTTPException(status_code=500, detail=f"Failed to create ZIP file: {str(e)}")


@app.get("/statistics")
async def get_statistics():
    """Get processing statistics."""
    return batch_processor.get_processing_statistics()


@app.delete("/jobs/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a running job (if possible)."""
    job = batch_processor.get_job_status(job_id)
    
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job.status not in ["pending", "running"]:
        raise HTTPException(status_code=400, detail="Job cannot be cancelled")
    
    # Note: Actual cancellation logic would need to be implemented
    # in the batch processor to handle graceful shutdown
    
    return {"message": "Job cancellation requested", "job_id": job_id}


@app.post("/cleanup")
async def cleanup_old_jobs(max_age_hours: int = 24):
    """Clean up old completed jobs."""
    cleaned_count = batch_processor.cleanup_completed_jobs(max_age_hours)
    return {"message": f"Cleaned up {cleaned_count} old jobs"}


def run_server():
    """Run the API server."""
    uvicorn.run(
        "src.api.server:app",
        host=config.api.host,
        port=config.api.port,
        debug=config.api.debug,
        reload=config.api.debug
    )


if __name__ == "__main__":
    run_server()
