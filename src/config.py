"""Configuration management for ATS CV Generator."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List
from pydantic import BaseModel, Field, validator

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # python-dotenv not installed, skip loading .env file
    pass


class GeminiConfig(BaseModel):
    """Gemini API configuration."""
    api_key: str = Field(default="", description="Gemini API key")
    model: str = Field(default="gemini-pro-vision", description="Gemini model name")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delays: List[int] = Field(default=[1, 2, 4], description="Retry delays in seconds")
    rate_limit: int = Field(default=60, description="Requests per minute")
    timeout: int = Field(default=30, description="Request timeout in seconds")

    @validator('api_key', pre=True, always=True)
    def get_api_key(cls, v):
        """Get API key from environment if not provided."""
        return v or os.getenv('GEMINI_API_KEY', '')


class TemplateConfig(BaseModel):
    """Template configuration."""
    max_count: int = Field(default=50, description="Maximum number of templates")
    max_file_size_mb: int = Field(default=10, description="Maximum file size in MB")
    supported_formats: List[str] = Field(default=["jpg", "jpeg"], description="Supported formats")
    categories: List[str] = Field(
        default=["simple", "complex", "creative", "corporate", "minimalist", "industry_specific"],
        description="Template categories"
    )


class GenerationConfig(BaseModel):
    """Generation configuration."""
    default_distribution: Dict[str, int] = Field(
        default={"high_quality": 80, "edge_cases": 10, "malicious": 10},
        description="Default distribution percentages"
    )
    max_total_count: int = Field(default=10000, description="Maximum total resumes to generate")
    min_job_description_length: int = Field(default=100, description="Minimum job description length")
    max_job_description_length: int = Field(default=3000, description="Maximum job description length")

    @validator('default_distribution')
    def validate_distribution(cls, v):
        """Validate that distribution percentages sum to 100."""
        if sum(v.values()) != 100:
            raise ValueError("Distribution percentages must sum to 100")
        return v


class ProcessingConfig(BaseModel):
    """Processing configuration."""
    max_concurrent_threads: int = Field(default=4, description="Maximum concurrent threads")
    batch_size: int = Field(default=10, description="Batch processing size")
    progress_update_interval: int = Field(default=5, description="Progress update interval in seconds")


class PDFConfig(BaseModel):
    """PDF generation configuration."""
    page_format: str = Field(default="A4", description="Page format")
    margin: str = Field(default="0.5in", description="Page margins")
    print_background: bool = Field(default=True, description="Print background graphics")
    wait_for_fonts: bool = Field(default=True, description="Wait for fonts to load")
    timeout: int = Field(default=30, description="PDF generation timeout in seconds")


class OutputConfig(BaseModel):
    """Output configuration."""
    base_directory: str = Field(default="./generated_resumes", description="Base output directory")
    subdirectories: Dict[str, str] = Field(
        default={
            "high_quality": "high_quality",
            "edge_cases": "edge_cases", 
            "malicious": "malicious",
            "templates": "templates_used"
        },
        description="Subdirectory names"
    )
    report_filename: str = Field(default="generation_report.json", description="Report filename")


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    file: str = Field(default="cv_generator.log", description="Log file name")
    max_file_size_mb: int = Field(default=10, description="Maximum log file size in MB")
    backup_count: int = Field(default=5, description="Number of backup log files")


class APIConfig(BaseModel):
    """API configuration."""
    host: str = Field(default="0.0.0.0", description="API host")
    port: int = Field(default=8000, description="API port")
    debug: bool = Field(default=False, description="Debug mode")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    max_upload_size_mb: int = Field(default=100, description="Maximum upload size in MB")


class Config(BaseModel):
    """Main configuration class."""
    gemini: GeminiConfig = Field(default_factory=GeminiConfig)
    templates: TemplateConfig = Field(default_factory=TemplateConfig)
    generation: GenerationConfig = Field(default_factory=GenerationConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    pdf: PDFConfig = Field(default_factory=PDFConfig)
    output: OutputConfig = Field(default_factory=OutputConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    api: APIConfig = Field(default_factory=APIConfig)


def load_config(config_path: str = "config.yaml") -> Config:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    
    if config_file.exists():
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        return Config(**config_data)
    else:
        # Return default configuration
        return Config()


# Global configuration instance
config = load_config()
