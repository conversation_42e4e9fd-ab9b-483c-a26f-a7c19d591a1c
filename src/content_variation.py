"""Content variation engine for generating unique resume content."""

import random
import hashlib
from typing import List, Dict, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class PersonProfile:
    """Profile data for a generated person."""
    first_name: str
    last_name: str
    email: str
    phone: str
    location: str
    linkedin: str
    github: Optional[str] = None


@dataclass
class ExperienceEntry:
    """Work experience entry."""
    company: str
    position: str
    duration: str
    description: List[str]
    location: str


@dataclass
class EducationEntry:
    """Education entry."""
    institution: str
    degree: str
    field: str
    graduation_year: str
    location: str
    gpa: Optional[str] = None


class ContentVariationEngine:
    """Generates varied content for resumes to ensure uniqueness."""
    
    def __init__(self):
        self.used_combinations = set()
        self._initialize_data_pools()
    
    def _initialize_data_pools(self):
        """Initialize pools of data for generating varied content."""
        
        # Name pools
        self.first_names = [
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Katherine", "Lauren", "Madison",
            "Natalie", "Olivia", "Rachel", "Samantha", "Victoria"
        ]
        
        self.last_names = [
            "Anderson", "Brown", "Davis", "Garcia", "Johnson", "Jones", "Martinez",
            "Miller", "Moore", "Rodriguez", "Smith", "Taylor", "Thomas", "Wilson",
            "Clark", "Lewis", "Robinson", "Walker", "Young", "Allen", "King", "Wright",
            "Lopez", "Hill", "Scott", "Green", "Adams", "Baker", "Gonzalez", "Nelson",
            "Carter", "Mitchell", "Perez", "Roberts", "Turner", "Phillips", "Campbell",
            "Parker", "Evans", "Edwards", "Collins", "Stewart", "Sanchez", "Morris",
            "Rogers", "Reed", "Cook", "Morgan", "Bell", "Murphy", "Bailey", "Rivera",
            "Cooper", "Richardson", "Cox", "Howard", "Ward", "Torres", "Peterson",
            "Gray", "Ramirez", "James", "Watson", "Brooks", "Kelly", "Sanders", "Price"
        ]
        
        # Location pools
        self.cities = [
            ("New York", "NY"), ("Los Angeles", "CA"), ("Chicago", "IL"), ("Houston", "TX"),
            ("Phoenix", "AZ"), ("Philadelphia", "PA"), ("San Antonio", "TX"), ("San Diego", "CA"),
            ("Dallas", "TX"), ("San Jose", "CA"), ("Austin", "TX"), ("Jacksonville", "FL"),
            ("Fort Worth", "TX"), ("Columbus", "OH"), ("Charlotte", "NC"), ("San Francisco", "CA"),
            ("Indianapolis", "IN"), ("Seattle", "WA"), ("Denver", "CO"), ("Washington", "DC"),
            ("Boston", "MA"), ("El Paso", "TX"), ("Nashville", "TN"), ("Detroit", "MI"),
            ("Oklahoma City", "OK"), ("Portland", "OR"), ("Las Vegas", "NV"), ("Memphis", "TN"),
            ("Louisville", "KY"), ("Baltimore", "MD"), ("Milwaukee", "WI"), ("Albuquerque", "NM"),
            ("Tucson", "AZ"), ("Fresno", "CA"), ("Sacramento", "CA"), ("Kansas City", "MO"),
            ("Mesa", "AZ"), ("Atlanta", "GA"), ("Omaha", "NE"), ("Colorado Springs", "CO"),
            ("Raleigh", "NC"), ("Miami", "FL"), ("Virginia Beach", "VA"), ("Oakland", "CA"),
            ("Minneapolis", "MN"), ("Tulsa", "OK"), ("Arlington", "TX"), ("Tampa", "FL")
        ]
        
        # Company pools by industry
        self.companies = {
            "tech": [
                "TechFlow Solutions", "DataStream Corp", "CloudVision Inc", "NextGen Systems",
                "InnovateTech", "DigitalBridge", "CodeCraft Solutions", "ByteForge",
                "CyberNova", "TechPulse", "DevStream", "CloudSphere", "DataForge",
                "TechNexus", "DigitalCore", "InnovateLabs", "CodeWave", "TechVault"
            ],
            "finance": [
                "Capital Dynamics", "Financial Horizons", "Investment Partners", "Wealth Management Group",
                "Strategic Finance", "Portfolio Solutions", "Asset Management Corp", "Financial Advisors Inc",
                "Capital Growth Partners", "Investment Strategies", "Wealth Builders", "Financial Planning Group"
            ],
            "healthcare": [
                "HealthTech Solutions", "Medical Innovations", "Healthcare Partners", "Wellness Corp",
                "MedTech Systems", "Health Solutions Group", "Medical Technology Inc", "Healthcare Dynamics"
            ],
            "consulting": [
                "Strategic Consulting", "Business Solutions Group", "Management Partners", "Advisory Services",
                "Consulting Excellence", "Strategic Advisors", "Business Dynamics", "Consulting Innovations"
            ]
        }
        
        # Skills pools by category
        self.skills = {
            "programming": [
                "Python", "JavaScript", "Java", "C++", "C#", "Go", "Rust", "TypeScript",
                "PHP", "Ruby", "Swift", "Kotlin", "Scala", "R", "MATLAB", "SQL"
            ],
            "web": [
                "React", "Angular", "Vue.js", "Node.js", "Express", "Django", "Flask",
                "Spring Boot", "ASP.NET", "Laravel", "Ruby on Rails", "Next.js"
            ],
            "data": [
                "Machine Learning", "Data Analysis", "Statistics", "Pandas", "NumPy",
                "TensorFlow", "PyTorch", "Scikit-learn", "Tableau", "Power BI", "Excel"
            ],
            "cloud": [
                "AWS", "Azure", "Google Cloud", "Docker", "Kubernetes", "Terraform",
                "Jenkins", "CI/CD", "DevOps", "Microservices", "Serverless"
            ],
            "soft": [
                "Leadership", "Communication", "Problem Solving", "Team Collaboration",
                "Project Management", "Critical Thinking", "Adaptability", "Time Management"
            ]
        }
        
        # Education institutions
        self.universities = [
            "University of California, Berkeley", "Stanford University", "MIT",
            "Harvard University", "Carnegie Mellon University", "University of Washington",
            "Georgia Institute of Technology", "University of Texas at Austin",
            "University of Michigan", "Cornell University", "Princeton University",
            "Yale University", "Columbia University", "University of Pennsylvania",
            "Northwestern University", "Duke University", "Vanderbilt University",
            "Rice University", "Emory University", "University of Southern California"
        ]
        
        self.degrees = [
            "Bachelor of Science", "Bachelor of Arts", "Master of Science",
            "Master of Arts", "Master of Business Administration", "Bachelor of Engineering"
        ]
        
        self.fields = [
            "Computer Science", "Software Engineering", "Data Science", "Information Technology",
            "Business Administration", "Marketing", "Finance", "Economics", "Psychology",
            "Communications", "Engineering", "Mathematics", "Statistics", "Physics"
        ]
    
    def generate_unique_profile(self, seed: Optional[str] = None) -> PersonProfile:
        """Generate a unique person profile."""
        if seed:
            random.seed(seed)
        
        # Generate until we get a unique combination
        max_attempts = 100
        for _ in range(max_attempts):
            first_name = random.choice(self.first_names)
            last_name = random.choice(self.last_names)
            
            # Create a combination key
            combo_key = f"{first_name}_{last_name}"
            if combo_key not in self.used_combinations:
                self.used_combinations.add(combo_key)
                break
        else:
            # If we can't find a unique combination, add a number
            first_name = random.choice(self.first_names)
            last_name = random.choice(self.last_names)
            combo_key = f"{first_name}_{last_name}_{len(self.used_combinations)}"
            self.used_combinations.add(combo_key)
        
        city, state = random.choice(self.cities)
        
        # Generate email variations
        email_formats = [
            f"{first_name.lower()}.{last_name.lower()}@gmail.com",
            f"{first_name.lower()}{last_name.lower()}@outlook.com",
            f"{first_name[0].lower()}{last_name.lower()}@yahoo.com",
            f"{first_name.lower()}_{last_name.lower()}@hotmail.com"
        ]
        
        # Generate phone number
        area_codes = ["555", "123", "456", "789", "321", "654", "987"]
        phone = f"({random.choice(area_codes)}) {random.randint(100, 999)}-{random.randint(1000, 9999)}"
        
        # LinkedIn profile
        linkedin = f"linkedin.com/in/{first_name.lower()}-{last_name.lower()}"
        
        # GitHub (optional)
        github = None
        if random.random() < 0.7:  # 70% chance of having GitHub
            github = f"github.com/{first_name.lower()}{last_name.lower()}"
        
        return PersonProfile(
            first_name=first_name,
            last_name=last_name,
            email=random.choice(email_formats),
            phone=phone,
            location=f"{city}, {state}",
            linkedin=linkedin,
            github=github
        )
    
    def generate_varied_experience(self, num_jobs: int = None, industry: str = "tech") -> List[ExperienceEntry]:
        """Generate varied work experience entries."""
        if num_jobs is None:
            num_jobs = random.randint(2, 5)
        
        experiences = []
        companies = self.companies.get(industry, self.companies["tech"])
        
        # Job titles by seniority level
        job_titles = {
            "junior": ["Junior Developer", "Software Engineer I", "Associate Developer", "Entry-level Analyst"],
            "mid": ["Software Engineer", "Senior Developer", "Full Stack Developer", "Data Scientist"],
            "senior": ["Senior Software Engineer", "Lead Developer", "Principal Engineer", "Engineering Manager"],
            "executive": ["Director of Engineering", "VP of Technology", "CTO", "Head of Development"]
        }
        
        current_year = datetime.now().year
        
        for i in range(num_jobs):
            # Determine seniority based on position (most recent = most senior)
            if i == 0:  # Most recent job
                level = random.choice(["mid", "senior"])
            elif i == 1:
                level = random.choice(["junior", "mid"])
            else:
                level = "junior"
            
            company = random.choice(companies)
            position = random.choice(job_titles[level])
            
            # Generate duration (more recent jobs are longer)
            if i == 0:  # Current/most recent job
                start_year = current_year - random.randint(1, 3)
                duration = f"{start_year} - Present"
            else:
                duration_years = random.randint(1, 3)
                end_year = current_year - i * 2
                start_year = end_year - duration_years
                duration = f"{start_year} - {end_year}"
            
            # Generate job descriptions
            descriptions = self._generate_job_descriptions(level, industry)
            
            city, state = random.choice(self.cities)
            
            experiences.append(ExperienceEntry(
                company=company,
                position=position,
                duration=duration,
                description=descriptions,
                location=f"{city}, {state}"
            ))
        
        return experiences
    
    def _generate_job_descriptions(self, level: str, industry: str) -> List[str]:
        """Generate job description bullet points."""
        base_descriptions = {
            "junior": [
                "Developed and maintained web applications using modern frameworks",
                "Collaborated with senior developers on feature implementation",
                "Participated in code reviews and testing processes",
                "Assisted in debugging and troubleshooting application issues"
            ],
            "mid": [
                "Led development of key product features serving thousands of users",
                "Implemented automated testing strategies improving code quality by 40%",
                "Mentored junior developers and conducted technical interviews",
                "Optimized application performance resulting in 25% faster load times"
            ],
            "senior": [
                "Architected and implemented scalable microservices handling 1M+ requests daily",
                "Led cross-functional teams of 5-8 engineers on critical product initiatives",
                "Established engineering best practices and development workflows",
                "Reduced system downtime by 60% through improved monitoring and alerting"
            ]
        }
        
        descriptions = base_descriptions.get(level, base_descriptions["mid"])
        return random.sample(descriptions, min(3, len(descriptions)))
    
    def generate_varied_education(self) -> List[EducationEntry]:
        """Generate varied education entries."""
        num_entries = random.randint(1, 2)
        education = []
        
        for i in range(num_entries):
            institution = random.choice(self.universities)
            degree = random.choice(self.degrees)
            field = random.choice(self.fields)
            
            # Generate graduation year
            current_year = datetime.now().year
            grad_year = str(current_year - random.randint(2, 10))
            
            # GPA (optional)
            gpa = None
            if random.random() < 0.6:  # 60% chance of including GPA
                gpa = f"{random.uniform(3.2, 4.0):.2f}"
            
            city, state = random.choice(self.cities)
            
            education.append(EducationEntry(
                institution=institution,
                degree=degree,
                field=field,
                graduation_year=grad_year,
                gpa=gpa,
                location=f"{city}, {state}"
            ))
        
        return education
    
    def generate_varied_skills(self, num_skills: int = None) -> Dict[str, List[str]]:
        """Generate varied skill sets."""
        if num_skills is None:
            num_skills = random.randint(12, 20)
        
        # Ensure we have a good mix of skill types
        skill_distribution = {
            "programming": random.randint(3, 6),
            "web": random.randint(2, 4),
            "data": random.randint(1, 3),
            "cloud": random.randint(2, 4),
            "soft": random.randint(3, 5)
        }
        
        selected_skills = {}
        for category, count in skill_distribution.items():
            available_skills = self.skills[category]
            selected = random.sample(available_skills, min(count, len(available_skills)))
            selected_skills[category] = selected
        
        return selected_skills
