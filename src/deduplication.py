"""Content deduplication and uniqueness validation for generated resumes."""

import hashlib
import re
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
import logging


@dataclass
class ContentFingerprint:
    """Fingerprint of resume content for deduplication."""
    full_hash: str
    text_hash: str
    structure_hash: str
    name_hash: str
    experience_hash: str
    education_hash: str
    skills_hash: str


@dataclass
class SimilarityResult:
    """Result of similarity comparison between resumes."""
    similarity_score: float
    is_duplicate: bool
    similar_sections: List[str]
    differences: Dict[str, str]


class ContentDeduplicator:
    """Handles content deduplication and uniqueness validation."""
    
    def __init__(self, similarity_threshold: float = 0.85):
        """Initialize deduplicator with similarity threshold."""
        self.similarity_threshold = similarity_threshold
        self.content_fingerprints: Dict[str, ContentFingerprint] = {}
        self.content_hashes: Set[str] = set()
        self.text_hashes: Set[str] = set()
        self.name_hashes: Set[str] = set()
        self.logger = logging.getLogger(__name__)
        
        # Track statistics
        self.stats = {
            "total_processed": 0,
            "duplicates_found": 0,
            "similar_content": 0,
            "unique_content": 0
        }
    
    def extract_content_fingerprint(self, html_content: str, resume_id: str) -> ContentFingerprint:
        """Extract comprehensive fingerprint from resume content."""
        
        # Extract text content (remove HTML tags)
        text_content = self._extract_text_from_html(html_content)
        
        # Extract specific sections
        name = self._extract_name(text_content)
        experience = self._extract_experience_section(text_content)
        education = self._extract_education_section(text_content)
        skills = self._extract_skills_section(text_content)
        
        # Extract HTML structure (tags and classes)
        structure = self._extract_html_structure(html_content)
        
        # Calculate hashes
        full_hash = hashlib.sha256(html_content.encode('utf-8')).hexdigest()
        text_hash = hashlib.sha256(text_content.encode('utf-8')).hexdigest()
        structure_hash = hashlib.sha256(structure.encode('utf-8')).hexdigest()
        name_hash = hashlib.sha256(name.encode('utf-8')).hexdigest()
        experience_hash = hashlib.sha256(experience.encode('utf-8')).hexdigest()
        education_hash = hashlib.sha256(education.encode('utf-8')).hexdigest()
        skills_hash = hashlib.sha256(skills.encode('utf-8')).hexdigest()
        
        fingerprint = ContentFingerprint(
            full_hash=full_hash,
            text_hash=text_hash,
            structure_hash=structure_hash,
            name_hash=name_hash,
            experience_hash=experience_hash,
            education_hash=education_hash,
            skills_hash=skills_hash
        )
        
        # Store fingerprint
        self.content_fingerprints[resume_id] = fingerprint
        
        return fingerprint
    
    def check_for_duplicates(self, html_content: str, resume_id: str) -> Tuple[bool, List[str]]:
        """Check if content is duplicate or too similar to existing content."""
        
        fingerprint = self.extract_content_fingerprint(html_content, resume_id)
        duplicates = []
        
        # Check for exact duplicates
        if fingerprint.full_hash in self.content_hashes:
            self.stats["duplicates_found"] += 1
            return True, ["exact_duplicate"]
        
        # Check for text content duplicates
        if fingerprint.text_hash in self.text_hashes:
            self.stats["duplicates_found"] += 1
            return True, ["text_duplicate"]
        
        # Check for name duplicates
        if fingerprint.name_hash in self.name_hashes:
            duplicates.append("name_duplicate")
        
        # Check for high similarity with existing content
        similar_resumes = self._find_similar_content(html_content, resume_id)
        if similar_resumes:
            self.stats["similar_content"] += 1
            duplicates.extend([f"similar_to_{rid}" for rid in similar_resumes])
        
        # Update tracking sets
        self.content_hashes.add(fingerprint.full_hash)
        self.text_hashes.add(fingerprint.text_hash)
        self.name_hashes.add(fingerprint.name_hash)
        self.stats["total_processed"] += 1
        
        if duplicates:
            return True, duplicates
        else:
            self.stats["unique_content"] += 1
            return False, []
    
    def _find_similar_content(self, html_content: str, current_resume_id: str) -> List[str]:
        """Find resumes with similar content."""
        similar_resumes = []
        current_text = self._extract_text_from_html(html_content)
        
        for resume_id, fingerprint in self.content_fingerprints.items():
            if resume_id == current_resume_id:
                continue
            
            # Get stored content for comparison (we'd need to store this)
            # For now, we'll use a simplified approach with section hashes
            similarity_score = self._calculate_section_similarity(
                self.content_fingerprints[current_resume_id], fingerprint
            )
            
            if similarity_score > self.similarity_threshold:
                similar_resumes.append(resume_id)
        
        return similar_resumes
    
    def _calculate_section_similarity(self, fp1: ContentFingerprint, fp2: ContentFingerprint) -> float:
        """Calculate similarity score between two content fingerprints."""
        
        # Count matching sections
        matches = 0
        total_sections = 6  # name, experience, education, skills, structure, text
        
        if fp1.name_hash == fp2.name_hash:
            matches += 1
        if fp1.experience_hash == fp2.experience_hash:
            matches += 1
        if fp1.education_hash == fp2.education_hash:
            matches += 1
        if fp1.skills_hash == fp2.skills_hash:
            matches += 1
        if fp1.structure_hash == fp2.structure_hash:
            matches += 1
        if fp1.text_hash == fp2.text_hash:
            matches += 1
        
        return matches / total_sections
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """Extract plain text from HTML content."""
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', ' ', html_content)
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters for comparison
        text = re.sub(r'[^\w\s]', ' ', text)
        return text.strip().lower()
    
    def _extract_html_structure(self, html_content: str) -> str:
        """Extract HTML structure (tags and classes) for comparison."""
        # Extract all HTML tags with their classes
        tags = re.findall(r'<(\w+)(?:\s+class=["\']([^"\']*)["\'])?[^>]*>', html_content)
        structure_elements = []
        
        for tag, class_attr in tags:
            if class_attr:
                structure_elements.append(f"{tag}.{class_attr}")
            else:
                structure_elements.append(tag)
        
        return ' '.join(sorted(set(structure_elements)))
    
    def _extract_name(self, text_content: str) -> str:
        """Extract name from text content."""
        # Look for patterns that might be names (first few words, capitalized)
        words = text_content.split()
        name_candidates = []
        
        for i, word in enumerate(words[:10]):  # Check first 10 words
            if word.istitle() and len(word) > 1:
                name_candidates.append(word)
                if len(name_candidates) >= 2:  # Assume first and last name
                    break
        
        return ' '.join(name_candidates[:2])
    
    def _extract_experience_section(self, text_content: str) -> str:
        """Extract experience section from text content."""
        # Look for experience-related keywords and extract surrounding text
        experience_keywords = ['experience', 'work', 'employment', 'career', 'position', 'job']
        
        for keyword in experience_keywords:
            pattern = rf'.*{keyword}.*?(?=education|skills|contact|$)'
            match = re.search(pattern, text_content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(0).strip()
        
        return ""
    
    def _extract_education_section(self, text_content: str) -> str:
        """Extract education section from text content."""
        education_keywords = ['education', 'degree', 'university', 'college', 'school']
        
        for keyword in education_keywords:
            pattern = rf'.*{keyword}.*?(?=experience|skills|contact|$)'
            match = re.search(pattern, text_content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(0).strip()
        
        return ""
    
    def _extract_skills_section(self, text_content: str) -> str:
        """Extract skills section from text content."""
        skills_keywords = ['skills', 'technologies', 'competencies', 'abilities']
        
        for keyword in skills_keywords:
            pattern = rf'.*{keyword}.*?(?=experience|education|contact|$)'
            match = re.search(pattern, text_content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(0).strip()
        
        return ""
    
    def get_uniqueness_report(self) -> Dict[str, any]:
        """Generate a report on content uniqueness."""
        total = self.stats["total_processed"]
        
        if total == 0:
            return {"error": "No content processed"}
        
        return {
            "total_processed": total,
            "unique_content": self.stats["unique_content"],
            "duplicates_found": self.stats["duplicates_found"],
            "similar_content": self.stats["similar_content"],
            "uniqueness_rate": (self.stats["unique_content"] / total) * 100,
            "duplicate_rate": (self.stats["duplicates_found"] / total) * 100,
            "similarity_rate": (self.stats["similar_content"] / total) * 100,
            "unique_names": len(self.name_hashes),
            "unique_text_content": len(self.text_hashes),
            "unique_full_content": len(self.content_hashes)
        }
    
    def clear_cache(self):
        """Clear all cached fingerprints and hashes."""
        self.content_fingerprints.clear()
        self.content_hashes.clear()
        self.text_hashes.clear()
        self.name_hashes.clear()
        self.stats = {
            "total_processed": 0,
            "duplicates_found": 0,
            "similar_content": 0,
            "unique_content": 0
        }
