"""Layout-based template system for generating resumes from text descriptions."""

from typing import Dict, List, Any
from .models import ResumeLayout


class LayoutTemplateGenerator:
    """Generate resume templates based on layout descriptions."""
    
    def __init__(self):
        self.layout_definitions = {
            ResumeLayout.CLASSIC_CHRONOLOGICAL: {
                "name": "Classic / Chronological Resume",
                "description": "Clean header with name + contact info, work experience in reverse chronological order, education + skills in side sections",
                "characteristics": [
                    "Traditional single-column layout",
                    "Header with name and contact information",
                    "Work experience section with reverse chronological order",
                    "Education and skills in separate sections",
                    "Professional, conservative styling",
                    "High ATS compatibility"
                ],
                "css_framework": "classic",
                "structure": "header-main-sidebar"
            },
            ResumeLayout.MODERN_SIDEBAR: {
                "name": "Modern Resume (with sidebar)",
                "description": "Left sidebar for contact info, skills, links; right section for work experience + achievements, often with icons and light colors",
                "characteristics": [
                    "Two-column layout with left sidebar",
                    "Contact info and skills in sidebar",
                    "Main content area for experience",
                    "Modern typography and spacing",
                    "Light color accents",
                    "Icon usage for visual appeal"
                ],
                "css_framework": "modern",
                "structure": "sidebar-main"
            },
            ResumeLayout.MINIMALIST: {
                "name": "Minimalist Resume",
                "description": "Very plain, lots of whitespace, only text with no icons or heavy styling, very ATS-friendly",
                "characteristics": [
                    "Minimal visual elements",
                    "Abundant whitespace",
                    "Simple typography",
                    "No icons or graphics",
                    "Black text on white background",
                    "Maximum ATS compatibility"
                ],
                "css_framework": "minimalist",
                "structure": "simple-linear"
            },
            ResumeLayout.FUNCTIONAL_SKILLS: {
                "name": "Functional / Skills-based Resume",
                "description": "Focuses on skills and competencies at the top, experience summarized instead of detailed, used for career changers",
                "characteristics": [
                    "Skills section prominently featured",
                    "Competency-based organization",
                    "Summarized work experience",
                    "Achievement-focused content",
                    "Good for career transitions",
                    "Emphasizes transferable skills"
                ],
                "css_framework": "functional",
                "structure": "skills-first"
            },
            ResumeLayout.HYBRID_COMBINATION: {
                "name": "Hybrid / Combination Resume",
                "description": "Mix of skills section + chronological experience, common for tech, product, or design roles",
                "characteristics": [
                    "Combined skills and chronological approach",
                    "Technical skills highlighted",
                    "Detailed work experience",
                    "Project showcases",
                    "Industry-specific sections",
                    "Balanced layout"
                ],
                "css_framework": "hybrid",
                "structure": "skills-experience-combo"
            }
        }
    
    def get_layout_info(self, layout: ResumeLayout) -> Dict[str, Any]:
        """Get detailed information about a layout."""
        return self.layout_definitions.get(layout, {})
    
    def get_all_layouts(self) -> Dict[ResumeLayout, Dict[str, Any]]:
        """Get all available layouts with their descriptions."""
        return self.layout_definitions
    
    def generate_layout_prompt(self, layout: ResumeLayout, job_description: str) -> str:
        """Generate a prompt for the AI to create a resume with the specified layout."""
        layout_info = self.layout_definitions.get(layout)
        if not layout_info:
            raise ValueError(f"Unknown layout: {layout}")
        
        prompt = f"""Create a professional resume using the {layout_info['name']} format.

Layout Description: {layout_info['description']}

Key Characteristics to implement:
{chr(10).join(f"- {char}" for char in layout_info['characteristics'])}

Job Description Context:
{job_description}

Requirements:
1. Generate realistic professional content that matches the job requirements
2. Use the specific layout structure: {layout_info['structure']}
3. Apply the {layout_info['css_framework']} styling approach
4. Include appropriate sections: Contact Info, Professional Summary, Work Experience, Education, Skills
5. Ensure content is relevant to the job description
6. Make it ATS-friendly while following the layout style

CRITICAL OUTPUT REQUIREMENTS:
- Output ONLY the raw HTML code starting with <!DOCTYPE html>
- Do NOT include any markdown formatting like ```html or ```
- Do NOT include any explanations, comments, or additional text
- Do NOT add phrases like "Key improvements and explanations:" or similar
- The response must start immediately with <!DOCTYPE html> and end with </html>
- No text before or after the HTML code

Create a complete HTML resume that follows this layout exactly."""
        
        return prompt
    
    def get_css_template(self, layout: ResumeLayout) -> str:
        """Get CSS template for the specified layout."""
        css_templates = {
            ResumeLayout.CLASSIC_CHRONOLOGICAL: self._get_classic_css(),
            ResumeLayout.MODERN_SIDEBAR: self._get_modern_css(),
            ResumeLayout.MINIMALIST: self._get_minimalist_css(),
            ResumeLayout.FUNCTIONAL_SKILLS: self._get_functional_css(),
            ResumeLayout.HYBRID_COMBINATION: self._get_hybrid_css()
        }
        return css_templates.get(layout, self._get_classic_css())
    
    def _get_classic_css(self) -> str:
        """Classic chronological resume CSS."""
        return """
        body { font-family: 'Times New Roman', serif; font-size: 11pt; line-height: 1.4; margin: 0.5in; }
        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
        .name { font-size: 18pt; font-weight: bold; margin-bottom: 5px; }
        .contact { font-size: 10pt; }
        .section { margin-bottom: 20px; }
        .section-title { font-size: 12pt; font-weight: bold; text-transform: uppercase; border-bottom: 1px solid #000; margin-bottom: 10px; }
        .job-title { font-weight: bold; }
        .company { font-style: italic; }
        .dates { float: right; font-style: italic; }
        """
    
    def _get_modern_css(self) -> str:
        """Modern sidebar resume CSS."""
        return """
        body { font-family: 'Arial', sans-serif; font-size: 10pt; margin: 0; display: flex; }
        .sidebar { width: 30%; background: #f8f9fa; padding: 20px; }
        .main-content { width: 70%; padding: 20px; }
        .name { font-size: 24pt; color: #2c3e50; margin-bottom: 10px; }
        .section-title { color: #3498db; font-size: 12pt; font-weight: bold; margin: 15px 0 8px 0; }
        .skill-item { background: #ecf0f1; padding: 5px 10px; margin: 3px 0; border-radius: 3px; }
        """
    
    def _get_minimalist_css(self) -> str:
        """Minimalist resume CSS."""
        return """
        body { font-family: 'Arial', sans-serif; font-size: 11pt; line-height: 1.6; margin: 1in; color: #000; }
        .header { margin-bottom: 30px; }
        .name { font-size: 20pt; margin-bottom: 10px; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 12pt; font-weight: bold; margin-bottom: 15px; }
        .job-entry { margin-bottom: 15px; }
        """
    
    def _get_functional_css(self) -> str:
        """Functional skills-based resume CSS."""
        return """
        body { font-family: 'Arial', sans-serif; font-size: 10pt; margin: 0.75in; }
        .skills-section { background: #f9f9f9; padding: 15px; margin-bottom: 20px; }
        .skill-category { margin-bottom: 10px; }
        .skill-category-title { font-weight: bold; color: #2c3e50; }
        .section-title { font-size: 12pt; font-weight: bold; color: #34495e; margin-bottom: 10px; }
        """
    
    def _get_hybrid_css(self) -> str:
        """Hybrid combination resume CSS."""
        return """
        body { font-family: 'Arial', sans-serif; font-size: 10pt; margin: 0.75in; }
        .top-section { display: flex; margin-bottom: 20px; }
        .skills-column { width: 40%; padding-right: 20px; }
        .summary-column { width: 60%; }
        .section-title { font-size: 12pt; font-weight: bold; color: #2c3e50; margin-bottom: 8px; }
        .tech-skills { background: #ecf0f1; padding: 10px; border-left: 3px solid #3498db; }
        """
    
    def get_layout_statistics(self) -> Dict[str, Any]:
        """Get statistics about available layouts."""
        return {
            "total_layouts": len(self.layout_definitions),
            "layout_types": list(self.layout_definitions.keys()),
            "layout_names": [info["name"] for info in self.layout_definitions.values()],
            "ats_friendly_layouts": [
                layout for layout, info in self.layout_definitions.items()
                if "ATS" in str(info.get("characteristics", []))
            ]
        }
