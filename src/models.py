"""Data models for ATS CV Generator."""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
from pydantic import BaseModel, Field, validator, model_validator
from enum import Enum


class ResumeType(str, Enum):
    """Resume type enumeration."""
    HIGH_QUALITY = "high_quality"
    EDGE_CASES = "edge_cases"
    MALICIOUS = "malicious"


class TemplateCategory(str, Enum):
    """Template category enumeration."""
    SIMPLE = "simple"
    COMPLEX = "complex"
    CREATIVE = "creative"
    CORPORATE = "corporate"
    MINIMALIST = "minimalist"
    INDUSTRY_SPECIFIC = "industry_specific"


class ResumeLayout(str, Enum):
    """Resume layout types for text-based template descriptions."""
    CLASSIC_CHRONOLOGICAL = "classic_chronological"
    MODERN_SIDEBAR = "modern_sidebar"
    MINIMALIST = "minimalist"
    FUNCTIONAL_SKILLS = "functional_skills"
    HYBRID_COMBINATION = "hybrid_combination"


class GenerationRequest(BaseModel):
    """Request model for resume generation."""
    job_description: str = Field(..., min_length=100, max_length=3000)
    total_count: int = Field(..., gt=0, le=10000)
    distribution: Dict[str, int] = Field(...)
    template_paths: Optional[List[str]] = Field(None, min_items=1, max_items=50)
    layout_descriptions: Optional[List[ResumeLayout]] = Field(None, min_items=1, max_items=5)
    output_directory: Optional[str] = Field(None)
    
    @validator('distribution')
    def validate_distribution(cls, v):
        """Validate distribution percentages."""
        if sum(v.values()) != 100:
            raise ValueError("Distribution percentages must sum to 100")
        
        required_keys = {"high_quality", "edge_cases", "malicious"}
        if not required_keys.issubset(v.keys()):
            raise ValueError(f"Distribution must contain keys: {required_keys}")
        
        for key, value in v.items():
            if not isinstance(value, int) or value < 0:
                raise ValueError(f"Distribution value for {key} must be a non-negative integer")
        
        return v
    
    @model_validator(mode='after')
    def validate_template_or_layout(self):
        """Validate that either template_paths or layout_descriptions is provided."""
        if not self.template_paths and not self.layout_descriptions:
            raise ValueError("Either template_paths or layout_descriptions must be provided")

        if self.template_paths and self.layout_descriptions:
            raise ValueError("Cannot specify both template_paths and layout_descriptions")

        # Validate template paths if provided
        if self.template_paths:
            for path in self.template_paths:
                file_path = Path(path)
                if not file_path.exists():
                    raise ValueError(f"Template file does not exist: {path}")
                if file_path.suffix.lower() not in ['.jpg', '.jpeg']:
                    raise ValueError(f"Template file must be JPEG format: {path}")

        return self

    @validator('layout_descriptions')
    def validate_layout_descriptions(cls, v, values):
        """Validate layout descriptions."""
        template_paths = values.get('template_paths')

        # Either template_paths or layout_descriptions must be provided
        if not v and not template_paths:
            raise ValueError("Either template_paths or layout_descriptions must be provided")

        return v


class Template(BaseModel):
    """Template model."""
    path: str
    category: Optional[TemplateCategory] = None
    file_size_mb: float
    dimensions: Optional[tuple] = None
    usage_count: int = 0


class GeneratedResume(BaseModel):
    """Generated resume model."""
    id: str
    type: ResumeType
    template_used: str
    html_content: str
    pdf_path: Optional[str] = None
    generation_time: datetime
    file_size_bytes: Optional[int] = None
    page_count: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None


class GenerationJob(BaseModel):
    """Generation job model."""
    job_id: str
    request: GenerationRequest
    status: str = "pending"  # pending, running, completed, failed
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    total_resumes: int = 0
    completed_resumes: int = 0
    failed_resumes: int = 0
    output_directory: Optional[str] = None
    error_message: Optional[str] = None


class GenerationReport(BaseModel):
    """Generation report model."""
    job_id: str
    request_parameters: Dict[str, Any]
    generation_statistics: Dict[str, Any]
    template_usage: Dict[str, int]
    success_rates: Dict[str, float]
    processing_time: float
    output_paths: Dict[str, Any]  # Changed from Dict[str, str] to Dict[str, Any] to support nested structures
    errors: List[str]
    created_at: datetime = Field(default_factory=datetime.now)


class ResumeContent(BaseModel):
    """Resume content structure."""
    personal_info: Dict[str, str]
    experience: List[Dict[str, Any]]
    education: List[Dict[str, Any]]
    skills: List[str]
    additional_sections: Optional[Dict[str, Any]] = None


class ValidationResult(BaseModel):
    """Validation result model."""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
