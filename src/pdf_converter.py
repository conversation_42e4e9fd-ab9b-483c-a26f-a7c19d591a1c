"""PDF conversion pipeline using <PERSON><PERSON>peteer for HTML to PDF conversion."""

import asyncio
import logging
import tempfile
from typing import Optional, Dict, Any, List
from pathlib import Path
import json
import subprocess
import os

try:
    from pyppeteer import launch
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False

from .config import config
from .models import GeneratedResume, ValidationResult
from .utils import format_file_size, sanitize_filename, generate_unique_filename, calculate_content_hash


class PDFConverter:
    """Handles HTML to PDF conversion using Puppeteer."""
    
    def __init__(self):
        """Initialize PDF converter."""
        self.logger = logging.getLogger(__name__)
        self.browser = None
        self.conversion_stats = {
            "total_conversions": 0,
            "successful": 0,
            "failed": 0,
            "total_size_bytes": 0
        }
        
        if not PYPPETEER_AVAILABLE:
            self.logger.warning("pyppeteer not available, falling back to subprocess method")
    
    async def __aenter__(self):
        """Async context manager entry."""
        if PYPPETEER_AVAILABLE:
            await self._start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if PYPPETEER_AVAILABLE and self.browser:
            await self._stop_browser()
    
    async def _start_browser(self):
        """Start Puppeteer browser instance."""
        if not PYPPETEER_AVAILABLE:
            return
        
        try:
            self.browser = await launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps'
                ]
            )
            self.logger.debug("Puppeteer browser started")
        except Exception as e:
            self.logger.error(f"Failed to start browser: {str(e)}")
            raise
    
    async def _stop_browser(self):
        """Stop Puppeteer browser instance."""
        if self.browser:
            try:
                await self.browser.close()
                self.logger.debug("Puppeteer browser stopped")
            except Exception as e:
                self.logger.error(f"Error stopping browser: {str(e)}")
    
    async def convert_html_to_pdf(
        self, 
        html_content: str, 
        output_path: str,
        options: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Convert HTML content to PDF."""
        self.conversion_stats["total_conversions"] += 1
        
        # Use default options if none provided
        if options is None:
            options = self._get_default_pdf_options()
        
        try:
            if PYPPETEER_AVAILABLE and self.browser:
                result = await self._convert_with_pyppeteer(html_content, output_path, options)
            else:
                result = await self._convert_with_subprocess(html_content, output_path, options)
            
            if result.is_valid:
                self.conversion_stats["successful"] += 1
                # Update file size stats
                if Path(output_path).exists():
                    file_size = Path(output_path).stat().st_size
                    self.conversion_stats["total_size_bytes"] += file_size
            else:
                self.conversion_stats["failed"] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"PDF conversion failed: {str(e)}")
            self.conversion_stats["failed"] += 1
            return ValidationResult(
                is_valid=False,
                errors=[f"PDF conversion failed: {str(e)}"]
            )
    
    async def _convert_with_pyppeteer(
        self, 
        html_content: str, 
        output_path: str, 
        options: Dict[str, Any]
    ) -> ValidationResult:
        """Convert using pyppeteer."""
        try:
            page = await self.browser.newPage()
            
            # Set viewport for consistent rendering
            await page.setViewport({'width': 1200, 'height': 1600})
            
            # Load HTML content
            await page.setContent(html_content, waitUntil='networkidle0')
            
            # Wait for fonts to load
            if config.pdf.wait_for_fonts:
                await asyncio.sleep(1)
            
            # Generate PDF
            pdf_options = {
                'path': output_path,
                'format': config.pdf.page_format,
                'margin': {
                    'top': config.pdf.margin,
                    'right': config.pdf.margin,
                    'bottom': config.pdf.margin,
                    'left': config.pdf.margin
                },
                'printBackground': config.pdf.print_background,
                **options
            }
            
            await page.pdf(pdf_options)
            await page.close()
            
            # Validate generated PDF
            return self._validate_pdf(output_path)
            
        except Exception as e:
            self.logger.error(f"Pyppeteer conversion failed: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Pyppeteer conversion failed: {str(e)}"]
            )
    
    async def _convert_with_subprocess(
        self, 
        html_content: str, 
        output_path: str, 
        options: Dict[str, Any]
    ) -> ValidationResult:
        """Convert using subprocess (fallback method)."""
        try:
            # Create temporary HTML file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(html_content)
                temp_html_path = temp_file.name
            
            try:
                # Try wkhtmltopdf first
                result = await self._convert_with_wkhtmltopdf(temp_html_path, output_path, options)
                if result.is_valid:
                    return result
                
                # Fallback to other methods
                self.logger.warning("wkhtmltopdf failed, trying alternative methods")
                return await self._convert_with_chrome_headless(temp_html_path, output_path, options)
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_html_path)
                except:
                    pass
                    
        except Exception as e:
            self.logger.error(f"Subprocess conversion failed: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Subprocess conversion failed: {str(e)}"]
            )
    
    async def _convert_with_wkhtmltopdf(
        self, 
        html_path: str, 
        output_path: str, 
        options: Dict[str, Any]
    ) -> ValidationResult:
        """Convert using wkhtmltopdf."""
        try:
            cmd = [
                'wkhtmltopdf',
                '--page-size', config.pdf.page_format,
                '--margin-top', config.pdf.margin,
                '--margin-right', config.pdf.margin,
                '--margin-bottom', config.pdf.margin,
                '--margin-left', config.pdf.margin,
                '--enable-local-file-access',
                '--print-media-type',
                html_path,
                output_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=config.pdf.timeout
            )
            
            if process.returncode == 0:
                return self._validate_pdf(output_path)
            else:
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                return ValidationResult(
                    is_valid=False,
                    errors=[f"wkhtmltopdf failed: {error_msg}"]
                )
                
        except FileNotFoundError:
            return ValidationResult(
                is_valid=False,
                errors=["wkhtmltopdf not found"]
            )
        except asyncio.TimeoutError:
            return ValidationResult(
                is_valid=False,
                errors=["wkhtmltopdf timeout"]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"wkhtmltopdf error: {str(e)}"]
            )
    
    async def _convert_with_chrome_headless(
        self, 
        html_path: str, 
        output_path: str, 
        options: Dict[str, Any]
    ) -> ValidationResult:
        """Convert using Chrome headless."""
        try:
            cmd = [
                'google-chrome',
                '--headless',
                '--disable-gpu',
                '--no-sandbox',
                '--print-to-pdf=' + output_path,
                f'--print-to-pdf-no-header',
                f'file://{html_path}'
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=config.pdf.timeout
            )
            
            if process.returncode == 0 and Path(output_path).exists():
                return self._validate_pdf(output_path)
            else:
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                return ValidationResult(
                    is_valid=False,
                    errors=[f"Chrome headless failed: {error_msg}"]
                )
                
        except FileNotFoundError:
            return ValidationResult(
                is_valid=False,
                errors=["Chrome not found"]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Chrome headless error: {str(e)}"]
            )
    
    def _validate_pdf(self, pdf_path: str) -> ValidationResult:
        """Validate generated PDF file."""
        errors = []
        warnings = []
        
        pdf_file = Path(pdf_path)
        
        # Check if file exists
        if not pdf_file.exists():
            errors.append(f"PDF file not created: {pdf_path}")
            return ValidationResult(is_valid=False, errors=errors)
        
        # Check file size
        file_size = pdf_file.stat().st_size
        if file_size == 0:
            errors.append("PDF file is empty")
        elif file_size < 1024:  # Less than 1KB
            warnings.append(f"PDF file is very small: {format_file_size(file_size)}")
        elif file_size > 50 * 1024 * 1024:  # More than 50MB
            warnings.append(f"PDF file is very large: {format_file_size(file_size)}")
        
        # Basic PDF header check
        try:
            with open(pdf_path, 'rb') as f:
                header = f.read(8)
                if not header.startswith(b'%PDF-'):
                    errors.append("File does not appear to be a valid PDF")
        except Exception as e:
            errors.append(f"Failed to read PDF file: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _get_default_pdf_options(self) -> Dict[str, Any]:
        """Get default PDF generation options."""
        return {
            'displayHeaderFooter': False,
            'preferCSSPageSize': True,
            'scale': 1.0
        }
    
    async def convert_resumes_batch(
        self, 
        resumes: List[GeneratedResume], 
        output_directory: str
    ) -> Dict[str, List[str]]:
        """Convert multiple resumes to PDF in batch."""
        self.logger.info(f"Starting batch PDF conversion for {len(resumes)} resumes")
        
        pdf_paths = {
            "high_quality": [],
            "edge_cases": [],
            "malicious": []
        }
        
        # Create PDF subdirectories
        base_path = Path(output_directory)
        for subdir in pdf_paths.keys():
            pdf_dir = base_path / subdir / "pdf"
            pdf_dir.mkdir(parents=True, exist_ok=True)
        
        successful_conversions = 0
        
        for index, resume in enumerate(resumes):
            if not resume.success or not resume.html_content:
                continue

            try:
                # Calculate content hash for uniqueness
                content_hash = calculate_content_hash(resume.html_content)

                # Create unique PDF filename
                pdf_filename = generate_unique_filename(
                    resume.id,
                    resume.generation_time,
                    resume.type.value,
                    index,
                    content_hash,
                    extension="pdf"
                )
                
                # Determine output path
                subdir = resume.type.value
                pdf_path = base_path / subdir / "pdf" / pdf_filename
                
                # Convert to PDF
                result = await self.convert_html_to_pdf(
                    resume.html_content,
                    str(pdf_path)
                )
                
                if result.is_valid:
                    pdf_paths[subdir].append(str(pdf_path))
                    resume.pdf_path = str(pdf_path)
                    
                    # Update resume with file info
                    if pdf_path.exists():
                        resume.file_size_bytes = pdf_path.stat().st_size
                    
                    successful_conversions += 1
                    self.logger.debug(f"Converted resume {resume.id} to PDF")
                else:
                    self.logger.error(f"Failed to convert resume {resume.id}: {result.errors}")
                    
            except Exception as e:
                self.logger.error(f"Error converting resume {resume.id} to PDF: {str(e)}")
        
        self.logger.info(f"Batch PDF conversion complete: {successful_conversions}/{len(resumes)} successful")
        return pdf_paths
    
    def get_conversion_statistics(self) -> Dict[str, Any]:
        """Get PDF conversion statistics."""
        stats = self.conversion_stats.copy()
        if stats["total_conversions"] > 0:
            stats["success_rate"] = stats["successful"] / stats["total_conversions"]
            stats["average_size_mb"] = stats["total_size_bytes"] / (1024 * 1024 * stats["successful"]) if stats["successful"] > 0 else 0
        return stats
    
    def reset_statistics(self) -> None:
        """Reset conversion statistics."""
        self.conversion_stats = {
            "total_conversions": 0,
            "successful": 0,
            "failed": 0,
            "total_size_bytes": 0
        }
