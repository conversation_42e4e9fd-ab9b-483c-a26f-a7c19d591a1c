"""Template processing and management system."""

import shutil
import random
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from PIL import Image
import json

from .config import config
from .models import Template, TemplateCategory, ValidationResult, ResumeLayout
from .utils import validate_template_file, get_file_hash, sanitize_filename
from .layout_templates import LayoutTemplateGenerator


class TemplateProcessor:
    """Handles template validation, processing, and distribution."""
    
    def __init__(self):
        """Initialize template processor."""
        self.logger = logging.getLogger(__name__)
        self.templates: List[Template] = []
        self.template_usage_stats: Dict[str, int] = {}
        self.layout_generator = LayoutTemplateGenerator()
        self.layout_templates: List[ResumeLayout] = []
    
    def validate_templates(self, template_paths: List[str]) -> ValidationResult:
        """Validate a list of template files."""
        errors = []
        warnings = []
        
        # Check count limits
        if len(template_paths) > config.templates.max_count:
            errors.append(f"Too many templates: {len(template_paths)} > {config.templates.max_count}")
        
        if len(template_paths) == 0:
            errors.append("No templates provided")
        
        # Validate each template
        valid_templates = []
        for template_path in template_paths:
            is_valid, message = validate_template_file(
                template_path, 
                config.templates.max_file_size_mb
            )
            
            if is_valid:
                valid_templates.append(template_path)
            else:
                errors.append(f"Template validation failed: {message}")
        
        # Check for duplicates
        template_hashes = {}
        for template_path in valid_templates:
            try:
                file_hash = get_file_hash(template_path)
                if file_hash in template_hashes:
                    warnings.append(f"Duplicate template detected: {template_path} and {template_hashes[file_hash]}")
                else:
                    template_hashes[file_hash] = template_path
            except Exception as e:
                errors.append(f"Failed to process template {template_path}: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def validate_layout_templates(self, layout_descriptions: List[ResumeLayout]) -> ValidationResult:
        """Validate layout template descriptions."""
        errors = []
        warnings = []

        # Check count limits
        if len(layout_descriptions) > 5:
            errors.append(f"Too many layout descriptions: {len(layout_descriptions)} > 5")

        # Validate each layout
        available_layouts = list(self.layout_generator.get_all_layouts().keys())
        for layout in layout_descriptions:
            if layout not in available_layouts:
                errors.append(f"Unknown layout type: {layout}")

        # Check for duplicates
        if len(layout_descriptions) != len(set(layout_descriptions)):
            warnings.append("Duplicate layout descriptions found")

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def load_templates(self, template_paths: List[str]) -> List[Template]:
        """Load and process template files."""
        templates = []
        
        for template_path in template_paths:
            try:
                template = self._process_template(template_path)
                templates.append(template)
                self.logger.debug(f"Loaded template: {template.path}")
            except Exception as e:
                self.logger.error(f"Failed to load template {template_path}: {str(e)}")
                raise
        
        self.templates = templates
        return templates

    def load_layout_templates(self, layout_descriptions: List[ResumeLayout]) -> List[Dict]:
        """Load layout templates from descriptions."""
        self.layout_templates = layout_descriptions
        layout_info = []

        for layout in layout_descriptions:
            info = self.layout_generator.get_layout_info(layout)
            layout_info.append({
                "layout": layout,
                "name": info.get("name", str(layout)),
                "description": info.get("description", ""),
                "characteristics": info.get("characteristics", []),
                "css_framework": info.get("css_framework", "default"),
                "structure": info.get("structure", "default")
            })

        self.logger.info(f"Loaded {len(layout_info)} layout templates")
        return layout_info

    def get_layout_prompt(self, layout: ResumeLayout, job_description: str) -> str:
        """Get AI prompt for generating a resume with the specified layout."""
        return self.layout_generator.generate_layout_prompt(layout, job_description)

    def get_layout_css(self, layout: ResumeLayout) -> str:
        """Get CSS template for the specified layout."""
        return self.layout_generator.get_css_template(layout)

    def distribute_layouts(self, total_count: int) -> List[ResumeLayout]:
        """Distribute layouts across the total count."""
        if not self.layout_templates:
            raise ValueError("No layout templates loaded")

        distributed_layouts = []
        layouts_per_template = total_count // len(self.layout_templates)
        remainder = total_count % len(self.layout_templates)

        for i, layout in enumerate(self.layout_templates):
            count = layouts_per_template
            if i < remainder:
                count += 1

            distributed_layouts.extend([layout] * count)

        # Shuffle for variety
        import random
        random.shuffle(distributed_layouts)

        return distributed_layouts
    
    def _process_template(self, template_path: str) -> Template:
        """Process a single template file."""
        path = Path(template_path)
        
        # Get file size
        file_size_bytes = path.stat().st_size
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        # Get image dimensions
        try:
            with Image.open(template_path) as img:
                dimensions = img.size
        except Exception as e:
            raise ValueError(f"Failed to read image dimensions: {str(e)}")
        
        # Auto-categorize template based on analysis
        category = self._categorize_template(template_path, dimensions)
        
        return Template(
            path=str(path.absolute()),
            category=category,
            file_size_mb=file_size_mb,
            dimensions=dimensions,
            usage_count=0
        )
    
    def _categorize_template(self, template_path: str, dimensions: Tuple[int, int]) -> TemplateCategory:
        """Auto-categorize template based on filename and dimensions."""
        filename = Path(template_path).name.lower()
        width, height = dimensions
        
        # Simple heuristics for categorization
        if any(word in filename for word in ['simple', 'basic', 'minimal']):
            return TemplateCategory.SIMPLE
        elif any(word in filename for word in ['complex', 'detailed', 'advanced']):
            return TemplateCategory.COMPLEX
        elif any(word in filename for word in ['creative', 'artistic', 'design']):
            return TemplateCategory.CREATIVE
        elif any(word in filename for word in ['corporate', 'business', 'professional']):
            return TemplateCategory.CORPORATE
        elif any(word in filename for word in ['minimal', 'clean', 'modern']):
            return TemplateCategory.MINIMALIST
        elif any(word in filename for word in ['tech', 'engineering', 'medical', 'legal']):
            return TemplateCategory.INDUSTRY_SPECIFIC
        else:
            # Default categorization based on dimensions
            aspect_ratio = width / height
            if aspect_ratio > 1.2:  # Wide format
                return TemplateCategory.CREATIVE
            elif width * height > 2000000:  # High resolution
                return TemplateCategory.COMPLEX
            else:
                return TemplateCategory.SIMPLE
    
    def distribute_templates(self, total_count: int) -> List[str]:
        """Distribute templates across resumes for variety."""
        if not self.templates:
            raise ValueError("No templates loaded")
        
        # Calculate how many times each template should be used
        templates_per_resume = max(1, len(self.templates) // total_count)
        
        distributed_templates = []
        template_paths = [t.path for t in self.templates]
        
        for i in range(total_count):
            # Use round-robin with some randomization
            base_index = i % len(template_paths)
            
            # Add some randomization to avoid strict patterns
            if random.random() < 0.3:  # 30% chance to use random template
                template_path = random.choice(template_paths)
            else:
                template_path = template_paths[base_index]
            
            distributed_templates.append(template_path)
            
            # Update usage stats
            self.template_usage_stats[template_path] = self.template_usage_stats.get(template_path, 0) + 1
        
        return distributed_templates
    
    def copy_templates_to_output(self, output_directory: str) -> Dict[str, str]:
        """Copy template files to output directory."""
        templates_dir = Path(output_directory) / config.output.subdirectories["templates"]
        templates_dir.mkdir(parents=True, exist_ok=True)
        
        copied_templates = {}
        
        for template in self.templates:
            source_path = Path(template.path)
            
            # Create safe filename
            safe_filename = sanitize_filename(source_path.name)
            dest_path = templates_dir / safe_filename
            
            # Handle filename conflicts
            counter = 1
            while dest_path.exists():
                name_parts = safe_filename.rsplit('.', 1)
                if len(name_parts) == 2:
                    safe_filename = f"{name_parts[0]}_{counter}.{name_parts[1]}"
                else:
                    safe_filename = f"{safe_filename}_{counter}"
                dest_path = templates_dir / safe_filename
                counter += 1
            
            # Copy file
            try:
                shutil.copy2(source_path, dest_path)
                copied_templates[template.path] = str(dest_path)
                self.logger.debug(f"Copied template: {source_path} -> {dest_path}")
            except Exception as e:
                self.logger.error(f"Failed to copy template {source_path}: {str(e)}")
                raise
        
        return copied_templates
    
    def get_template_statistics(self) -> Dict[str, any]:
        """Get template usage statistics."""
        if not self.templates:
            return {}
        
        stats = {
            "total_templates": len(self.templates),
            "categories": {},
            "usage_stats": self.template_usage_stats.copy(),
            "file_sizes": {},
            "dimensions": {}
        }
        
        # Category distribution
        for template in self.templates:
            category = template.category.value if template.category else "unknown"
            stats["categories"][category] = stats["categories"].get(category, 0) + 1
        
        # File size statistics
        file_sizes = [t.file_size_mb for t in self.templates]
        if file_sizes:
            stats["file_sizes"] = {
                "min_mb": min(file_sizes),
                "max_mb": max(file_sizes),
                "avg_mb": sum(file_sizes) / len(file_sizes),
                "total_mb": sum(file_sizes)
            }
        
        # Dimension statistics
        dimensions = [t.dimensions for t in self.templates if t.dimensions]
        if dimensions:
            widths = [d[0] for d in dimensions]
            heights = [d[1] for d in dimensions]
            stats["dimensions"] = {
                "min_width": min(widths),
                "max_width": max(widths),
                "min_height": min(heights),
                "max_height": max(heights),
                "avg_width": sum(widths) / len(widths),
                "avg_height": sum(heights) / len(heights)
            }
        
        return stats
    
    def save_template_metadata(self, output_directory: str) -> None:
        """Save template metadata to output directory."""
        metadata = {
            "templates": [
                {
                    "path": t.path,
                    "category": t.category.value if t.category else None,
                    "file_size_mb": t.file_size_mb,
                    "dimensions": t.dimensions,
                    "usage_count": self.template_usage_stats.get(t.path, 0)
                }
                for t in self.templates
            ],
            "statistics": self.get_template_statistics()
        }
        
        metadata_path = Path(output_directory) / "template_metadata.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Saved template metadata to {metadata_path}")
    
    def reset_usage_stats(self) -> None:
        """Reset template usage statistics."""
        self.template_usage_stats.clear()
    
    def get_template_by_path(self, template_path: str) -> Optional[Template]:
        """Get template object by path."""
        for template in self.templates:
            if template.path == template_path:
                return template
        return None
