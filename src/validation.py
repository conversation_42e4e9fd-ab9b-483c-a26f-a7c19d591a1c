"""Comprehensive validation system for ATS CV Generator."""

import re
import os
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from PIL import Image
import json

from .config import config
from .models import ValidationResult, GenerationRequest, ResumeType
from .utils import get_file_hash


class InputValidator:
    """Comprehensive input validation system."""
    
    def __init__(self):
        """Initialize validator."""
        self.validation_rules = {
            'job_description': self._validate_job_description,
            'total_count': self._validate_total_count,
            'distribution': self._validate_distribution,
            'template_paths': self._validate_template_paths,
            'output_directory': self._validate_output_directory
        }
    
    def validate_generation_request(self, request: GenerationRequest) -> ValidationResult:
        """Validate complete generation request."""
        errors = []
        warnings = []
        
        # Validate each field
        for field_name, validator in self.validation_rules.items():
            if hasattr(request, field_name):
                field_value = getattr(request, field_name)
                result = validator(field_value)
                errors.extend(result.errors)
                warnings.extend(result.warnings)
        
        # Cross-field validation
        cross_validation = self._validate_cross_fields(request)
        errors.extend(cross_validation.errors)
        warnings.extend(cross_validation.warnings)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_job_description(self, job_description: str) -> ValidationResult:
        """Validate job description."""
        errors = []
        warnings = []
        
        # Length validation
        min_length = config.generation.min_job_description_length
        max_length = config.generation.max_job_description_length
        
        if len(job_description) < min_length:
            errors.append(f"Job description too short: {len(job_description)} < {min_length}")
        
        if len(job_description) > max_length:
            errors.append(f"Job description too long: {len(job_description)} > {max_length}")
        
        # Content validation
        if not job_description.strip():
            errors.append("Job description cannot be empty or whitespace only")
        
        # Check for minimum meaningful content
        word_count = len(job_description.split())
        if word_count < 10:
            warnings.append(f"Job description has very few words: {word_count}")
        
        # Check for common job description elements
        common_elements = [
            'experience', 'skills', 'requirements', 'responsibilities',
            'qualifications', 'education', 'role', 'position'
        ]
        
        found_elements = sum(1 for element in common_elements 
                           if element.lower() in job_description.lower())
        
        if found_elements < 2:
            warnings.append("Job description may lack common job posting elements")
        
        # Check for suspicious content
        suspicious_patterns = [
            r'<script.*?>.*?</script>',  # Script tags
            r'javascript:',              # JavaScript URLs
            r'data:.*?base64',          # Data URLs
            r'<iframe.*?>',             # Iframes
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, job_description, re.IGNORECASE | re.DOTALL):
                warnings.append("Job description contains potentially suspicious content")
                break
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_total_count(self, total_count: int) -> ValidationResult:
        """Validate total count."""
        errors = []
        warnings = []
        
        if total_count <= 0:
            errors.append("Total count must be positive")
        
        if total_count > config.generation.max_total_count:
            errors.append(f"Total count too high: {total_count} > {config.generation.max_total_count}")
        
        # Performance warnings
        if total_count > 1000:
            warnings.append(f"Large generation request ({total_count} resumes) may take significant time")
        
        if total_count > 5000:
            warnings.append("Very large requests may require substantial system resources")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_distribution(self, distribution: Dict[str, int]) -> ValidationResult:
        """Validate distribution percentages."""
        errors = []
        warnings = []
        
        # Check required keys
        required_keys = {"high_quality", "edge_cases", "malicious"}
        if not required_keys.issubset(distribution.keys()):
            missing = required_keys - distribution.keys()
            errors.append(f"Missing required distribution keys: {missing}")
        
        # Check for extra keys
        extra_keys = set(distribution.keys()) - required_keys
        if extra_keys:
            warnings.append(f"Unknown distribution keys (will be ignored): {extra_keys}")
        
        # Validate values
        for key, value in distribution.items():
            if not isinstance(value, int):
                errors.append(f"Distribution value for '{key}' must be an integer")
            elif value < 0:
                errors.append(f"Distribution value for '{key}' cannot be negative")
            elif value > 100:
                errors.append(f"Distribution value for '{key}' cannot exceed 100")
        
        # Check sum
        total = sum(v for k, v in distribution.items() if k in required_keys)
        if total != 100:
            errors.append(f"Distribution percentages must sum to 100, got {total}")
        
        # Balance warnings
        if distribution.get("malicious", 0) > 50:
            warnings.append("High percentage of malicious resumes may not be suitable for all testing scenarios")
        
        if distribution.get("high_quality", 0) < 50:
            warnings.append("Low percentage of high-quality resumes may limit baseline testing")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_template_paths(self, template_paths: List[str]) -> ValidationResult:
        """Validate template file paths."""
        errors = []
        warnings = []
        
        # Check count
        if not template_paths:
            errors.append("No template paths provided")
            return ValidationResult(is_valid=False, errors=errors)
        
        if len(template_paths) > config.templates.max_count:
            errors.append(f"Too many templates: {len(template_paths)} > {config.templates.max_count}")
        
        # Validate each template
        valid_templates = []
        template_hashes = {}
        total_size = 0
        
        for i, template_path in enumerate(template_paths):
            path = Path(template_path)
            
            # Existence check
            if not path.exists():
                errors.append(f"Template file does not exist: {template_path}")
                continue
            
            if not path.is_file():
                errors.append(f"Template path is not a file: {template_path}")
                continue
            
            # Format check
            if path.suffix.lower() not in ['.jpg', '.jpeg']:
                errors.append(f"Template must be JPEG format: {template_path}")
                continue
            
            # Size check
            file_size = path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            total_size += file_size
            
            if file_size_mb > config.templates.max_file_size_mb:
                errors.append(f"Template file too large: {template_path} ({file_size_mb:.1f}MB > {config.templates.max_file_size_mb}MB)")
                continue
            
            # Image validation
            try:
                with Image.open(template_path) as img:
                    width, height = img.size
                    
                    # Dimension checks
                    if width < 100 or height < 100:
                        warnings.append(f"Template has very small dimensions: {template_path} ({width}x{height})")
                    
                    if width > 5000 or height > 5000:
                        warnings.append(f"Template has very large dimensions: {template_path} ({width}x{height})")
                    
                    # Aspect ratio check
                    aspect_ratio = width / height
                    if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                        warnings.append(f"Template has unusual aspect ratio: {template_path} ({aspect_ratio:.2f})")
                    
            except Exception as e:
                errors.append(f"Failed to read template image: {template_path} - {str(e)}")
                continue
            
            # Duplicate check
            try:
                file_hash = get_file_hash(template_path)
                if file_hash in template_hashes:
                    warnings.append(f"Duplicate template detected: {template_path} and {template_hashes[file_hash]}")
                else:
                    template_hashes[file_hash] = template_path
            except Exception as e:
                warnings.append(f"Failed to calculate hash for template: {template_path}")
            
            valid_templates.append(template_path)
        
        # Total size warning
        total_size_mb = total_size / (1024 * 1024)
        if total_size_mb > 100:
            warnings.append(f"Large total template size: {total_size_mb:.1f}MB")
        
        # Variety check
        if len(valid_templates) == 1:
            warnings.append("Only one template provided - limited variety in generated resumes")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_output_directory(self, output_directory: Optional[str]) -> ValidationResult:
        """Validate output directory."""
        errors = []
        warnings = []
        
        if output_directory is None:
            return ValidationResult(is_valid=True, errors=errors, warnings=warnings)
        
        path = Path(output_directory)
        
        # Check if parent directory exists and is writable
        parent = path.parent
        if not parent.exists():
            errors.append(f"Parent directory does not exist: {parent}")
        elif not os.access(parent, os.W_OK):
            errors.append(f"Parent directory is not writable: {parent}")
        
        # Check if path already exists
        if path.exists():
            if not path.is_dir():
                errors.append(f"Output path exists but is not a directory: {output_directory}")
            elif not os.access(path, os.W_OK):
                errors.append(f"Output directory is not writable: {output_directory}")
            else:
                # Check if directory is empty
                if any(path.iterdir()):
                    warnings.append(f"Output directory is not empty: {output_directory}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_cross_fields(self, request: GenerationRequest) -> ValidationResult:
        """Validate relationships between fields."""
        errors = []
        warnings = []
        
        # Check if template count is reasonable for resume count
        template_count = len(request.template_paths)
        resume_count = request.total_count
        
        if template_count > resume_count:
            warnings.append(f"More templates ({template_count}) than resumes ({resume_count}) - some templates will be unused")
        
        # Check distribution vs count
        min_count_per_type = min(
            int(resume_count * request.distribution["high_quality"] / 100),
            int(resume_count * request.distribution["edge_cases"] / 100),
            int(resume_count * request.distribution["malicious"] / 100)
        )
        
        if min_count_per_type == 0 and resume_count > 0:
            warnings.append("Some resume types will have 0 resumes due to small total count and distribution")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )


class ConfigValidator:
    """Validate configuration settings."""
    
    @staticmethod
    def validate_config() -> ValidationResult:
        """Validate current configuration."""
        errors = []
        warnings = []
        
        # Validate Gemini API key
        if not config.gemini.api_key:
            errors.append("Gemini API key is not configured")
        
        # Validate file paths
        if not Path(config.logging.file).parent.exists():
            warnings.append(f"Log file directory does not exist: {Path(config.logging.file).parent}")
        
        # Validate numeric ranges
        if config.processing.max_concurrent_threads <= 0:
            errors.append("max_concurrent_threads must be positive")
        
        if config.processing.max_concurrent_threads > 20:
            warnings.append("Very high concurrent thread count may cause resource issues")
        
        # Validate PDF settings
        valid_formats = ["A4", "Letter", "Legal", "A3", "A5"]
        if config.pdf.page_format not in valid_formats:
            warnings.append(f"Unusual PDF page format: {config.pdf.page_format}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
