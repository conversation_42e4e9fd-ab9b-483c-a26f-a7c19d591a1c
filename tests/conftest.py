"""Pytest configuration and fixtures."""

import pytest
import tempfile
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import os

from src.config import Config


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    if temp_path.exists():
        shutil.rmtree(temp_path)


@pytest.fixture
def sample_templates(temp_dir):
    """Create sample template images for testing."""
    templates = []
    
    # Create different types of templates
    template_configs = [
        {"name": "simple_template.jpg", "size": (800, 1000), "color": "white"},
        {"name": "complex_template.jpg", "size": (1200, 1600), "color": "lightgray"},
        {"name": "creative_template.jpg", "size": (900, 1200), "color": "lightblue"},
    ]
    
    for config in template_configs:
        template_path = temp_dir / config["name"]
        
        # Create image
        img = Image.new('RGB', config["size"], color=config["color"])
        draw = ImageDraw.Draw(img)
        
        # Add some basic content to make it look like a resume template
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Draw template sections
        draw.rectangle([50, 50, config["size"][0]-50, 150], outline="black", width=2)
        draw.text((60, 70), "HEADER SECTION", fill="black", font=font)
        
        draw.rectangle([50, 200, config["size"][0]-50, 400], outline="black", width=2)
        draw.text((60, 220), "EXPERIENCE SECTION", fill="black", font=font)
        
        draw.rectangle([50, 450, config["size"][0]-50, 600], outline="black", width=2)
        draw.text((60, 470), "EDUCATION SECTION", fill="black", font=font)
        
        # Save image
        img.save(template_path, 'JPEG', quality=85)
        templates.append(str(template_path))
    
    return templates


@pytest.fixture
def sample_job_description():
    """Sample job description for testing."""
    return """
    Software Engineer Position
    
    We are seeking a skilled Software Engineer to join our development team. 
    The ideal candidate will have experience with Python, JavaScript, and modern web frameworks.
    
    Requirements:
    - 3+ years of software development experience
    - Proficiency in Python and JavaScript
    - Experience with React, Node.js, or similar frameworks
    - Knowledge of database systems (SQL and NoSQL)
    - Understanding of version control systems (Git)
    - Strong problem-solving and communication skills
    
    Responsibilities:
    - Develop and maintain web applications
    - Collaborate with cross-functional teams
    - Write clean, maintainable code
    - Participate in code reviews
    - Troubleshoot and debug applications
    
    Education:
    - Bachelor's degree in Computer Science or related field
    - Relevant certifications are a plus
    
    This is a full-time position with competitive salary and benefits.
    """


@pytest.fixture
def sample_distribution():
    """Sample distribution for testing."""
    return {"high_quality": 70, "edge_cases": 20, "malicious": 10}


@pytest.fixture
def test_config():
    """Test configuration with safe defaults."""
    return Config(
        gemini={
            "api_key": "test_key",
            "model": "gemini-pro-vision",
            "max_retries": 2,
            "rate_limit": 10
        },
        generation={
            "max_total_count": 1000,
            "min_job_description_length": 50,
            "max_job_description_length": 2000
        },
        processing={
            "max_concurrent_threads": 2
        }
    )


@pytest.fixture
def mock_gemini_response():
    """Mock Gemini API response."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Resume</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .section { margin-bottom: 15px; }
            .section h2 { border-bottom: 1px solid #ccc; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>John Doe</h1>
            <p>Software Engineer</p>
            <p><EMAIL> | (555) 123-4567</p>
        </div>
        
        <div class="section">
            <h2>Experience</h2>
            <h3>Senior Software Engineer - Tech Company (2020-Present)</h3>
            <ul>
                <li>Developed web applications using Python and JavaScript</li>
                <li>Led team of 3 developers on major project</li>
                <li>Improved application performance by 40%</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>Education</h2>
            <h3>Bachelor of Science in Computer Science</h3>
            <p>University of Technology (2016-2020)</p>
        </div>
        
        <div class="section">
            <h2>Skills</h2>
            <ul>
                <li>Python, JavaScript, React, Node.js</li>
                <li>SQL, MongoDB, PostgreSQL</li>
                <li>Git, Docker, AWS</li>
            </ul>
        </div>
    </body>
    </html>
    """


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment."""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    
    yield
    
    # Cleanup
    if "TESTING" in os.environ:
        del os.environ["TESTING"]


@pytest.fixture
def mock_pdf_file(temp_dir):
    """Create a mock PDF file for testing."""
    pdf_path = temp_dir / "test_resume.pdf"
    
    # Create a minimal PDF-like file (just for testing file operations)
    with open(pdf_path, 'wb') as f:
        f.write(b'%PDF-1.4\n')  # PDF header
        f.write(b'1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n')
        f.write(b'2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n')
        f.write(b'3 0 obj\n<< /Type /Page /Parent 2 0 R >>\nendobj\n')
        f.write(b'xref\n0 4\n0000000000 65535 f \n')
        f.write(b'trailer\n<< /Size 4 /Root 1 0 R >>\n')
        f.write(b'startxref\n0\n%%EOF\n')
    
    return str(pdf_path)


class MockGeminiClient:
    """Mock Gemini client for testing."""
    
    def __init__(self, mock_response=None):
        self.mock_response = mock_response or """
        <!DOCTYPE html>
        <html><head><title>Test Resume</title></head>
        <body><h1>Test Resume</h1><p>Mock content</p></body>
        </html>
        """
        self.call_count = 0
    
    async def generate_resume_html(self, template_path, job_description, resume_type):
        """Mock resume generation."""
        self.call_count += 1
        return self.mock_response
    
    async def test_connection(self):
        """Mock connection test."""
        return True
    
    def get_template_info(self, template_path):
        """Mock template info."""
        return {
            "path": template_path,
            "filename": Path(template_path).name,
            "file_size_bytes": 1024,
            "file_size_mb": 0.001,
            "dimensions": (800, 1000),
            "mode": "RGB",
            "hash": "mock_hash"
        }


@pytest.fixture
def mock_gemini_client(mock_gemini_response):
    """Provide mock Gemini client."""
    return MockGeminiClient(mock_gemini_response)
