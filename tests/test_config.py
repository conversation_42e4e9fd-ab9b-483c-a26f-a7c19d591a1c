"""Tests for configuration management."""

import pytest
import tempfile
import yaml
from pathlib import Path

from src.config import Config, load_config, GeminiConfig, GenerationConfig


class TestConfig:
    """Test configuration loading and validation."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = Config()
        
        assert config.gemini.model == "gemini-pro-vision"
        assert config.gemini.max_retries == 3
        assert config.generation.max_total_count == 10000
        assert config.processing.max_concurrent_threads == 4
    
    def test_gemini_config_validation(self):
        """Test Gemini configuration validation."""
        # Valid config
        config = GeminiConfig(api_key="test_key")
        assert config.api_key == "test_key"
        
        # Environment variable fallback
        import os
        os.environ["GEMINI_API_KEY"] = "env_key"
        config = GeminiConfig()
        assert config.api_key == "env_key"
        
        # Clean up
        if "GEMINI_API_KEY" in os.environ:
            del os.environ["GEMINI_API_KEY"]
    
    def test_generation_config_validation(self):
        """Test generation configuration validation."""
        # Valid distribution
        config = GenerationConfig(
            default_distribution={"high_quality": 70, "edge_cases": 20, "malicious": 10}
        )
        assert sum(config.default_distribution.values()) == 100
        
        # Invalid distribution
        with pytest.raises(ValueError, match="Distribution percentages must sum to 100"):
            GenerationConfig(
                default_distribution={"high_quality": 70, "edge_cases": 20, "malicious": 5}
            )
    
    def test_load_config_from_file(self):
        """Test loading configuration from YAML file."""
        config_data = {
            "gemini": {
                "api_key": "test_key",
                "model": "custom-model"
            },
            "generation": {
                "max_total_count": 5000
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            config = load_config(config_path)
            assert config.gemini.api_key == "test_key"
            assert config.gemini.model == "custom-model"
            assert config.generation.max_total_count == 5000
        finally:
            Path(config_path).unlink()
    
    def test_load_nonexistent_config(self):
        """Test loading configuration when file doesn't exist."""
        config = load_config("nonexistent.yaml")
        # Should return default configuration
        assert isinstance(config, Config)
        assert config.gemini.model == "gemini-pro-vision"


class TestConfigValidation:
    """Test configuration validation."""
    
    def test_validate_processing_config(self):
        """Test processing configuration validation."""
        from src.config import ProcessingConfig
        
        # Valid config
        config = ProcessingConfig(max_concurrent_threads=4)
        assert config.max_concurrent_threads == 4
        
        # Test defaults
        config = ProcessingConfig()
        assert config.max_concurrent_threads == 4
        assert config.batch_size == 10
    
    def test_validate_pdf_config(self):
        """Test PDF configuration validation."""
        from src.config import PDFConfig
        
        config = PDFConfig()
        assert config.page_format == "A4"
        assert config.margin == "0.5in"
        assert config.print_background is True
    
    def test_validate_output_config(self):
        """Test output configuration validation."""
        from src.config import OutputConfig
        
        config = OutputConfig()
        assert "high_quality" in config.subdirectories
        assert "edge_cases" in config.subdirectories
        assert "malicious" in config.subdirectories
        assert config.report_filename == "generation_report.json"
