"""Tests for utility functions."""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime

from src.utils import (
    calculate_distribution_counts, validate_template_file,
    create_output_directory, get_file_hash, save_json_report,
    format_file_size, format_duration, sanitize_filename,
    estimate_processing_time, validate_distribution_percentages
)


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_calculate_distribution_counts(self):
        """Test distribution count calculation."""
        distribution = {"high_quality": 70, "edge_cases": 20, "malicious": 10}
        
        # Test exact division
        counts = calculate_distribution_counts(100, distribution)
        assert counts["high_quality"] == 70
        assert counts["edge_cases"] == 20
        assert counts["malicious"] == 10
        assert sum(counts.values()) == 100
        
        # Test with remainder
        counts = calculate_distribution_counts(103, distribution)
        assert sum(counts.values()) == 103
        # Last type should get the remainder
        assert counts["malicious"] >= 10
    
    def test_validate_template_file_valid(self):
        """Test valid template file validation."""
        # Create a temporary JPEG file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            from PIL import Image
            img = Image.new('RGB', (100, 100), color='white')
            img.save(f.name, 'JPEG')
            temp_path = f.name
        
        try:
            is_valid, message = validate_template_file(temp_path)
            assert is_valid
            assert message == "Valid"
        finally:
            Path(temp_path).unlink()
    
    def test_validate_template_file_nonexistent(self):
        """Test nonexistent template file."""
        is_valid, message = validate_template_file("/nonexistent/file.jpg")
        assert not is_valid
        assert "does not exist" in message
    
    def test_validate_template_file_wrong_format(self):
        """Test wrong file format."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"not an image")
            temp_path = f.name
        
        try:
            is_valid, message = validate_template_file(temp_path)
            assert not is_valid
            assert "must be JPEG format" in message
        finally:
            Path(temp_path).unlink()
    
    def test_create_output_directory(self):
        """Test output directory creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = create_output_directory(temp_dir, "test_job_123")
            
            output_path = Path(output_dir)
            assert output_path.exists()
            assert output_path.is_dir()
            
            # Check subdirectories
            subdirs = ["high_quality", "edge_cases", "malicious", "templates_used"]
            for subdir in subdirs:
                assert (output_path / subdir).exists()
                assert (output_path / subdir).is_dir()
    
    def test_get_file_hash(self):
        """Test file hash calculation."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            f.write(b"test content")
            temp_path = f.name
        
        try:
            hash1 = get_file_hash(temp_path)
            hash2 = get_file_hash(temp_path)
            
            # Same file should produce same hash
            assert hash1 == hash2
            assert len(hash1) == 32  # MD5 hash length
        finally:
            Path(temp_path).unlink()
    
    def test_save_and_load_json_report(self):
        """Test JSON report saving and loading."""
        test_data = {
            "job_id": "test_123",
            "timestamp": datetime.now().isoformat(),
            "statistics": {"total": 100, "successful": 95}
        }
        
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            save_json_report(test_data, temp_path)
            
            # Verify file was created and contains correct data
            assert Path(temp_path).exists()
            
            with open(temp_path, 'r') as f:
                loaded_data = json.load(f)
            
            assert loaded_data["job_id"] == test_data["job_id"]
            assert loaded_data["statistics"]["total"] == 100
        finally:
            Path(temp_path).unlink()
    
    def test_format_file_size(self):
        """Test file size formatting."""
        assert format_file_size(512) == "512.0 B"
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(1024 * 1024) == "1.0 MB"
        assert format_file_size(1024 * 1024 * 1024) == "1.0 GB"
    
    def test_format_duration(self):
        """Test duration formatting."""
        assert format_duration(30) == "30.0s"
        assert format_duration(90) == "1.5m"
        assert format_duration(3600) == "1.0h"
        assert format_duration(7200) == "2.0h"
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Test invalid characters
        assert sanitize_filename("file<name>.txt") == "file_name_.txt"
        assert sanitize_filename("file:name.txt") == "file_name.txt"
        assert sanitize_filename("file|name.txt") == "file_name.txt"
        
        # Test length limit
        long_name = "a" * 300
        sanitized = sanitize_filename(long_name)
        assert len(sanitized) <= 255
    
    def test_estimate_processing_time(self):
        """Test processing time estimation."""
        # Test basic estimation
        time_estimate = estimate_processing_time(100, 4)
        assert time_estimate > 0
        assert isinstance(time_estimate, float)
        
        # More threads should reduce time
        time_1_thread = estimate_processing_time(100, 1)
        time_4_threads = estimate_processing_time(100, 4)
        assert time_1_thread > time_4_threads
    
    def test_validate_distribution_percentages(self):
        """Test distribution percentage validation."""
        # Valid distribution
        valid_dist = {"high_quality": 70, "edge_cases": 20, "malicious": 10}
        is_valid, message = validate_distribution_percentages(valid_dist)
        assert is_valid
        assert message == "Valid"
        
        # Invalid sum
        invalid_sum = {"high_quality": 70, "edge_cases": 20, "malicious": 5}
        is_valid, message = validate_distribution_percentages(invalid_sum)
        assert not is_valid
        assert "must equal 100" in message
        
        # Missing keys
        missing_keys = {"high_quality": 70, "edge_cases": 30}
        is_valid, message = validate_distribution_percentages(missing_keys)
        assert not is_valid
        assert "Missing required" in message
        
        # Invalid values
        invalid_values = {"high_quality": -10, "edge_cases": 60, "malicious": 50}
        is_valid, message = validate_distribution_percentages(invalid_values)
        assert not is_valid
        assert "non-negative integer" in message


class TestGenerationHelpers:
    """Test generation helper functions."""
    
    def test_generate_ids(self):
        """Test ID generation functions."""
        from src.utils import generate_job_id, generate_resume_id
        
        job_id1 = generate_job_id()
        job_id2 = generate_job_id()
        
        # IDs should be unique
        assert job_id1 != job_id2
        assert len(job_id1) > 0
        assert len(job_id2) > 0
        
        resume_id1 = generate_resume_id()
        resume_id2 = generate_resume_id()
        
        assert resume_id1 != resume_id2
        assert len(resume_id1) > 0
        assert len(resume_id2) > 0
