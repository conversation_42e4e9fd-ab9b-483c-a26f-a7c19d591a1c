"""Tests for input validation system."""

import pytest
import tempfile
from pathlib import Path
from PIL import Image

from src.validation import InputValidator, ConfigValidator
from src.models import GenerationRequest, ValidationResult


class TestInputValidator:
    """Test input validation functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.validator = InputValidator()
        
        # Create temporary test templates
        self.temp_dir = Path(tempfile.mkdtemp())
        self.template_paths = []
        
        for i in range(3):
            template_path = self.temp_dir / f"template_{i}.jpg"
            # Create a simple test image
            img = Image.new('RGB', (800, 1000), color='white')
            img.save(template_path, 'JPEG')
            self.template_paths.append(str(template_path))
    
    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_validate_job_description_valid(self):
        """Test valid job description validation."""
        job_desc = "Software Engineer position requiring Python, JavaScript, and database experience. " * 3
        result = self.validator._validate_job_description(job_desc)
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_job_description_too_short(self):
        """Test job description too short."""
        job_desc = "Short job"
        result = self.validator._validate_job_description(job_desc)
        
        assert not result.is_valid
        assert any("too short" in error for error in result.errors)
    
    def test_validate_job_description_too_long(self):
        """Test job description too long."""
        job_desc = "A" * 4000  # Exceeds max length
        result = self.validator._validate_job_description(job_desc)
        
        assert not result.is_valid
        assert any("too long" in error for error in result.errors)
    
    def test_validate_job_description_empty(self):
        """Test empty job description."""
        result = self.validator._validate_job_description("")
        
        assert not result.is_valid
        assert any("cannot be empty" in error for error in result.errors)
    
    def test_validate_job_description_warnings(self):
        """Test job description warnings."""
        job_desc = "Simple job posting without common elements."
        result = self.validator._validate_job_description(job_desc)
        
        # Should be valid but have warnings
        assert result.is_valid
        assert len(result.warnings) > 0
    
    def test_validate_total_count_valid(self):
        """Test valid total count."""
        result = self.validator._validate_total_count(100)
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_total_count_invalid(self):
        """Test invalid total count."""
        # Zero count
        result = self.validator._validate_total_count(0)
        assert not result.is_valid
        
        # Negative count
        result = self.validator._validate_total_count(-5)
        assert not result.is_valid
        
        # Too high count
        result = self.validator._validate_total_count(20000)
        assert not result.is_valid
    
    def test_validate_total_count_warnings(self):
        """Test total count warnings."""
        # Large count should generate warning
        result = self.validator._validate_total_count(2000)
        
        assert result.is_valid
        assert len(result.warnings) > 0
    
    def test_validate_distribution_valid(self):
        """Test valid distribution."""
        distribution = {"high_quality": 70, "edge_cases": 20, "malicious": 10}
        result = self.validator._validate_distribution(distribution)
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_distribution_invalid_sum(self):
        """Test distribution with invalid sum."""
        distribution = {"high_quality": 70, "edge_cases": 20, "malicious": 5}
        result = self.validator._validate_distribution(distribution)
        
        assert not result.is_valid
        assert any("must sum to 100" in error for error in result.errors)
    
    def test_validate_distribution_missing_keys(self):
        """Test distribution with missing keys."""
        distribution = {"high_quality": 70, "edge_cases": 30}
        result = self.validator._validate_distribution(distribution)
        
        assert not result.is_valid
        assert any("Missing required" in error for error in result.errors)
    
    def test_validate_distribution_invalid_values(self):
        """Test distribution with invalid values."""
        distribution = {"high_quality": -10, "edge_cases": 60, "malicious": 50}
        result = self.validator._validate_distribution(distribution)
        
        assert not result.is_valid
        assert any("cannot be negative" in error for error in result.errors)
    
    def test_validate_template_paths_valid(self):
        """Test valid template paths."""
        result = self.validator._validate_template_paths(self.template_paths)
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_template_paths_empty(self):
        """Test empty template paths."""
        result = self.validator._validate_template_paths([])
        
        assert not result.is_valid
        assert any("No template paths" in error for error in result.errors)
    
    def test_validate_template_paths_nonexistent(self):
        """Test nonexistent template paths."""
        result = self.validator._validate_template_paths(["/nonexistent/file.jpg"])
        
        assert not result.is_valid
        assert any("does not exist" in error for error in result.errors)
    
    def test_validate_template_paths_wrong_format(self):
        """Test template paths with wrong format."""
        # Create a non-JPEG file
        txt_file = self.temp_dir / "not_image.txt"
        txt_file.write_text("not an image")
        
        result = self.validator._validate_template_paths([str(txt_file)])
        
        assert not result.is_valid
        assert any("must be JPEG format" in error for error in result.errors)
    
    def test_validate_template_paths_too_many(self):
        """Test too many template paths."""
        # Create more templates than allowed
        many_paths = [str(self.temp_dir / f"template_{i}.jpg") for i in range(60)]
        
        result = self.validator._validate_template_paths(many_paths)
        
        assert not result.is_valid
        assert any("Too many templates" in error for error in result.errors)
    
    def test_validate_output_directory_valid(self):
        """Test valid output directory."""
        result = self.validator._validate_output_directory(str(self.temp_dir))
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_output_directory_none(self):
        """Test None output directory."""
        result = self.validator._validate_output_directory(None)
        
        assert result.is_valid  # None is acceptable
    
    def test_validate_generation_request_valid(self):
        """Test valid generation request."""
        request = GenerationRequest(
            job_description="Software Engineer position requiring Python and JavaScript experience. " * 5,
            total_count=100,
            distribution={"high_quality": 70, "edge_cases": 20, "malicious": 10},
            template_paths=self.template_paths
        )
        
        result = self.validator.validate_generation_request(request)
        
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_validate_generation_request_invalid(self):
        """Test invalid generation request."""
        request = GenerationRequest(
            job_description="Short",  # Too short
            total_count=0,  # Invalid count
            distribution={"high_quality": 50, "edge_cases": 30, "malicious": 10},  # Wrong sum
            template_paths=[]  # No templates
        )
        
        result = self.validator.validate_generation_request(request)
        
        assert not result.is_valid
        assert len(result.errors) > 0


class TestConfigValidator:
    """Test configuration validation."""
    
    def test_validate_config_valid(self):
        """Test valid configuration."""
        # Mock a valid API key
        import os
        os.environ["GEMINI_API_KEY"] = "test_key"
        
        try:
            result = ConfigValidator.validate_config()
            # Should be valid or have only warnings
            assert result.is_valid or len(result.errors) == 0
        finally:
            if "GEMINI_API_KEY" in os.environ:
                del os.environ["GEMINI_API_KEY"]
    
    def test_validate_config_missing_api_key(self):
        """Test configuration with missing API key."""
        # Ensure no API key is set
        import os
        if "GEMINI_API_KEY" in os.environ:
            del os.environ["GEMINI_API_KEY"]
        
        # Reset config to pick up environment change
        from src.config import config
        config.gemini.api_key = ""
        
        result = ConfigValidator.validate_config()
        
        assert not result.is_valid
        assert any("API key" in error for error in result.errors)
